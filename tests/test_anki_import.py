"""
Tests for the Anki import functionality.

This module tests the Anki .apkg file import system including
file validation, data parsing, and card conversion.
"""

import pytest
import tempfile
import zipfile
import sqlite3
import json
import os
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from pathlib import Path

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.importers import AnkiImporter
from srs.config import reset_config


class TestAnkiImporter:
    """Test the Anki importer functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_anki_importer_initialization(self):
        """Test Anki importer initialization."""
        importer = AnkiImporter()
        assert importer.temp_dir is None
        assert importer.anki_db is None
        assert importer.media_map == {}
    
    def test_get_supported_formats(self):
        """Test getting supported file formats."""
        importer = AnkiImporter()
        formats = importer.get_supported_formats()
        assert '.apkg' in formats
    
    def test_validate_file_invalid_extension(self):
        """Test file validation with invalid extension."""
        importer = AnkiImporter()
        assert not importer.validate_file('test.txt')
        assert not importer.validate_file('test.csv')
    
    def test_validate_file_nonexistent(self):
        """Test file validation with nonexistent file."""
        importer = AnkiImporter()
        assert not importer.validate_file('nonexistent.apkg')
    
    def create_mock_apkg(self, temp_dir: str, include_collection: bool = True) -> str:
        """Create a mock .apkg file for testing."""
        apkg_path = os.path.join(temp_dir, 'test.apkg')
        
        with zipfile.ZipFile(apkg_path, 'w') as zip_file:
            # Add media file
            media_data = {"0": "image.jpg", "1": "audio.mp3"}
            zip_file.writestr('media', json.dumps(media_data))
            
            if include_collection:
                # Create a mock collection.anki2 database
                db_path = os.path.join(temp_dir, 'collection.anki2')
                self._create_mock_anki_db(db_path)
                zip_file.write(db_path, 'collection.anki2')
        
        return apkg_path
    
    def _create_mock_anki_db(self, db_path: str):
        """Create a mock Anki database."""
        conn = sqlite3.connect(db_path)
        
        # Create basic Anki schema
        conn.execute('''
            CREATE TABLE decks (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                mtime_secs INTEGER NOT NULL,
                usn INTEGER NOT NULL,
                common BLOB NOT NULL,
                kind INTEGER NOT NULL
            )
        ''')
        
        conn.execute('''
            CREATE TABLE notes (
                id INTEGER PRIMARY KEY,
                guid TEXT NOT NULL UNIQUE,
                mid INTEGER NOT NULL,
                mod INTEGER NOT NULL,
                usn INTEGER NOT NULL,
                tags TEXT NOT NULL,
                flds TEXT NOT NULL,
                sfld TEXT NOT NULL,
                csum INTEGER NOT NULL,
                flags INTEGER NOT NULL,
                data TEXT NOT NULL
            )
        ''')
        
        conn.execute('''
            CREATE TABLE cards (
                id INTEGER PRIMARY KEY,
                nid INTEGER NOT NULL,
                did INTEGER NOT NULL,
                ord INTEGER NOT NULL,
                mod INTEGER NOT NULL,
                usn INTEGER NOT NULL,
                type INTEGER NOT NULL,
                queue INTEGER NOT NULL,
                due INTEGER NOT NULL,
                ivl INTEGER NOT NULL,
                factor INTEGER NOT NULL,
                reps INTEGER NOT NULL,
                lapses INTEGER NOT NULL,
                left INTEGER NOT NULL,
                odue INTEGER NOT NULL,
                odid INTEGER NOT NULL,
                flags INTEGER NOT NULL,
                data TEXT NOT NULL
            )
        ''')
        
        # Insert test data
        conn.execute("INSERT INTO decks (id, name, mtime_secs, usn, common, kind) VALUES (1, 'Test Deck', 1234567890, 0, '', 0)")
        
        conn.execute('''
            INSERT INTO notes (id, guid, mid, mod, usn, tags, flds, sfld, csum, flags, data)
            VALUES (1, 'test-guid-1', 1, 1234567890, 0, 'tag1 tag2', 'Front Text\x1fBack Text', 'Front Text', 123, 0, '')
        ''')
        
        conn.execute('''
            INSERT INTO notes (id, guid, mid, mod, usn, tags, flds, sfld, csum, flags, data)
            VALUES (2, 'test-guid-2', 1, 1234567890, 0, '', 'Question\x1fAnswer', 'Question', 456, 0, '')
        ''')
        
        conn.execute('''
            INSERT INTO cards (id, nid, did, ord, mod, usn, type, queue, due, ivl, factor, reps, lapses, left, odue, odid, flags, data)
            VALUES (1, 1, 1, 0, 1234567890, 0, 0, 0, 1, 0, 2500, 0, 0, 0, 0, 0, 0, '')
        ''')
        
        conn.execute('''
            INSERT INTO cards (id, nid, did, ord, mod, usn, type, queue, due, ivl, factor, reps, lapses, left, odue, odid, flags, data)
            VALUES (2, 2, 1, 0, 1234567890, 0, 2, 2, 100, 7, 2300, 3, 1, 0, 0, 0, 0, '')
        ''')
        
        conn.commit()
        conn.close()
    
    def test_validate_file_valid_apkg(self):
        """Test file validation with valid .apkg file."""
        importer = AnkiImporter()
        apkg_path = self.create_mock_apkg(self.temp_dir)
        
        assert importer.validate_file(apkg_path)
    
    def test_validate_file_invalid_apkg(self):
        """Test file validation with invalid .apkg file."""
        importer = AnkiImporter()
        apkg_path = self.create_mock_apkg(self.temp_dir, include_collection=False)
        
        assert not importer.validate_file(apkg_path)
    
    def test_dry_run_import(self):
        """Test dry run import functionality."""
        with patch('srs.models.get_database', return_value=self.db):
            importer = AnkiImporter()
            apkg_path = self.create_mock_apkg(self.temp_dir)
            
            result = importer.import_deck(apkg_path, 'Test Import', dry_run=True)
            
            assert result['dry_run'] is True
            assert result['target_deck'] == 'Test Import'
            assert result['total_cards'] == 2
            assert result['new_cards'] == 1
            assert result['review_cards'] == 1
            assert 'estimated_import_time' in result
    
    def test_actual_import(self):
        """Test actual import functionality."""
        with patch('srs.models.get_database', return_value=self.db):
            importer = AnkiImporter()
            apkg_path = self.create_mock_apkg(self.temp_dir)
            
            result = importer.import_deck(apkg_path, 'Test Import', dry_run=False)
            
            assert result['dry_run'] is False
            assert result['target_deck'] == 'Test Import'
            assert result['imported_cards'] > 0
            assert result['total_processed'] == 2
            
            # Verify deck was created
            deck = Deck.get_by_name('Test Import')
            assert deck is not None
    
    def test_import_with_merge(self):
        """Test import with merge option."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create existing deck
            existing_deck = Deck.create('Test Import')
            
            importer = AnkiImporter()
            apkg_path = self.create_mock_apkg(self.temp_dir)
            
            result = importer.import_deck(apkg_path, 'Test Import', merge=True)
            
            assert result['deck_id'] == existing_deck.id
            assert result['imported_cards'] > 0
    
    def test_import_without_merge_existing_deck(self):
        """Test import without merge when deck exists."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create existing deck
            Deck.create('Test Import')
            
            importer = AnkiImporter()
            apkg_path = self.create_mock_apkg(self.temp_dir)
            
            with pytest.raises(ValueError, match="already exists"):
                importer.import_deck(apkg_path, 'Test Import', merge=False)
    
    def test_extract_apkg(self):
        """Test .apkg file extraction."""
        importer = AnkiImporter()
        apkg_path = self.create_mock_apkg(self.temp_dir)
        
        importer._extract_apkg(apkg_path)
        
        assert importer.temp_dir is not None
        assert os.path.exists(importer.temp_dir)
        assert os.path.exists(os.path.join(importer.temp_dir, 'collection.anki2'))
        assert os.path.exists(os.path.join(importer.temp_dir, 'media'))
        assert importer.media_map == {"0": "image.jpg", "1": "audio.mp3"}
        
        importer._cleanup()
    
    def test_parse_anki_database(self):
        """Test Anki database parsing."""
        importer = AnkiImporter()
        apkg_path = self.create_mock_apkg(self.temp_dir)
        
        importer._extract_apkg(apkg_path)
        cards_data = importer._parse_anki_database()
        
        assert len(cards_data) == 2
        
        # Check first card (new card)
        card1 = cards_data[0]
        assert card1['card_type'] == 0  # New card
        assert card1['fields'] == ['Front Text', 'Back Text']
        assert card1['tags'] == ['tag1', 'tag2']
        
        # Check second card (review card)
        card2 = cards_data[1]
        assert card2['card_type'] == 2  # Review card
        assert card2['fields'] == ['Question', 'Answer']
        assert card2['repetitions'] == 3
        
        importer._cleanup()
    
    def test_extract_card_content(self):
        """Test card content extraction."""
        importer = AnkiImporter()
        
        card_data = {
            'fields': ['Front Text', 'Back Text', 'Extra Field']
        }
        
        front, back = importer._extract_card_content(card_data)
        
        assert front == 'Front Text'
        assert back == 'Back Text'
    
    def test_extract_card_content_insufficient_fields(self):
        """Test card content extraction with insufficient fields."""
        importer = AnkiImporter()
        
        card_data = {
            'fields': ['Only One Field']
        }
        
        front, back = importer._extract_card_content(card_data)
        
        assert front == ""
        assert back == ""
    
    def test_clean_html(self):
        """Test HTML cleaning functionality."""
        importer = AnkiImporter()
        
        html_content = '<b>Bold text</b> with &nbsp; spaces &amp; entities'
        cleaned = importer._clean_html(html_content)
        
        assert cleaned == 'Bold text with spaces & entities'
    
    def test_convert_scheduling_data(self):
        """Test Anki scheduling data conversion."""
        with patch('srs.models.get_database', return_value=self.db):
            importer = AnkiImporter()
            deck = Deck.create('Test Deck')
            card = Card.create(deck.id, 'front', 'back')
            
            anki_data = {
                'ease_factor': 2.3,
                'interval': 7,
                'repetitions': 3,
                'due': 100,  # Days since epoch
                'card_type': 2  # Review card
            }
            
            importer._convert_scheduling_data(card, anki_data)
            
            assert card.ease_factor == 2.3
            assert card.interval == 7
            assert card.repetitions == 3
            assert card.learning_state == 'graduated'
    
    def test_cleanup(self):
        """Test cleanup functionality."""
        importer = AnkiImporter()
        apkg_path = self.create_mock_apkg(self.temp_dir)
        
        importer._extract_apkg(apkg_path)
        temp_dir = importer.temp_dir
        
        assert os.path.exists(temp_dir)
        
        importer._cleanup()
        
        assert not os.path.exists(temp_dir)
        assert importer.anki_db is None
