"""
Tests for the advanced review filtering functionality.

This module tests the review filter system including difficulty-based,
interval-based, and state-based filtering.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timedelta

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.review import (
    ReviewFilter, create_filtered_review_session,
    create_hard_cards_session, create_easy_cards_session,
    create_new_cards_session, create_overdue_cards_session,
    create_learning_cards_session, _apply_review_filters,
    _get_card_type, _is_card_due, _is_card_overdue
)
from srs.config import reset_config


class TestReviewFilters:
    """Test the review filtering functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.db = Database(':memory:')
        reset_config()
        
        # Create test data
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck
            self.deck = Deck.create('Test Deck')
            
            # Create cards with different properties
            self.new_card = Card.create(self.deck.id, 'New Question', 'New Answer')
            self.new_card.difficulty = 0.5
            self.new_card.repetitions = 0
            self.new_card.interval = 1
            self.new_card.learning_state = 'new'
            self.new_card.save()
            
            self.easy_card = Card.create(self.deck.id, 'Easy Question', 'Easy Answer')
            self.easy_card.difficulty = 0.2
            self.easy_card.repetitions = 3
            self.easy_card.interval = 14
            self.easy_card.learning_state = 'graduated'
            self.easy_card.due_date = datetime.now() - timedelta(days=1)  # Due
            self.easy_card.save()
            
            self.hard_card = Card.create(self.deck.id, 'Hard Question', 'Hard Answer')
            self.hard_card.difficulty = 0.8
            self.hard_card.repetitions = 1
            self.hard_card.interval = 2
            self.hard_card.learning_state = 'learning'
            self.hard_card.due_date = datetime.now() + timedelta(days=1)  # Not due yet
            self.hard_card.save()
            
            self.overdue_card = Card.create(self.deck.id, 'Overdue Question', 'Overdue Answer')
            self.overdue_card.difficulty = 0.6
            self.overdue_card.repetitions = 2
            self.overdue_card.interval = 7
            self.overdue_card.learning_state = 'graduated'
            self.overdue_card.due_date = datetime.now() - timedelta(days=3)  # Overdue
            self.overdue_card.save()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_review_filter_initialization(self):
        """Test ReviewFilter initialization."""
        filter_config = ReviewFilter()
        assert filter_config.difficulty_min is None
        assert filter_config.difficulty_max is None
        assert filter_config.interval_min is None
        assert filter_config.interval_max is None
        assert filter_config.due_only is False
        assert filter_config.overdue_only is False
    
    def test_difficulty_filter_min(self):
        """Test filtering by minimum difficulty."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(difficulty_min=0.5)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include new_card (0.5), hard_card (0.8), overdue_card (0.6)
            # Should exclude easy_card (0.2)
            assert len(filtered) == 3
            card_difficulties = [card.difficulty for card in filtered]
            assert all(diff >= 0.5 for diff in card_difficulties)
    
    def test_difficulty_filter_max(self):
        """Test filtering by maximum difficulty."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(difficulty_max=0.6)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include easy_card (0.2), new_card (0.5), overdue_card (0.6)
            # Should exclude hard_card (0.8)
            assert len(filtered) == 3
            card_difficulties = [card.difficulty for card in filtered]
            assert all(diff <= 0.6 for diff in card_difficulties)
    
    def test_difficulty_filter_range(self):
        """Test filtering by difficulty range."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(difficulty_min=0.3, difficulty_max=0.7)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include new_card (0.5), overdue_card (0.6)
            # Should exclude easy_card (0.2), hard_card (0.8)
            assert len(filtered) == 2
            card_difficulties = [card.difficulty for card in filtered]
            assert all(0.3 <= diff <= 0.7 for diff in card_difficulties)
    
    def test_interval_filter(self):
        """Test filtering by interval."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(interval_min=5)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include easy_card (14), overdue_card (7)
            # Should exclude new_card (1), hard_card (2)
            assert len(filtered) == 2
            card_intervals = [card.interval for card in filtered]
            assert all(interval >= 5 for interval in card_intervals)
    
    def test_repetitions_filter(self):
        """Test filtering by repetitions."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(repetitions_min=2)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include easy_card (3), overdue_card (2)
            # Should exclude new_card (0), hard_card (1)
            assert len(filtered) == 2
            card_reps = [card.repetitions for card in filtered]
            assert all(reps >= 2 for reps in card_reps)
    
    def test_learning_state_filter(self):
        """Test filtering by learning state."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(learning_states=['graduated'])
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include easy_card, overdue_card (both graduated)
            # Should exclude new_card (new), hard_card (learning)
            assert len(filtered) == 2
            card_states = [getattr(card, 'learning_state', 'new') for card in filtered]
            assert all(state == 'graduated' for state in card_states)
    
    def test_due_only_filter(self):
        """Test filtering for due cards only."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(due_only=True)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include cards that are due (new_card, easy_card, overdue_card)
            # Should exclude hard_card (not due yet)
            assert len(filtered) >= 2  # At least new and overdue cards
    
    def test_overdue_only_filter(self):
        """Test filtering for overdue cards only."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(overdue_only=True)
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include only overdue cards
            assert len(filtered) >= 1  # At least the overdue card
            for card in filtered:
                assert _is_card_overdue(card, datetime.now())
    
    def test_combined_filters(self):
        """Test combining multiple filters."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(
                difficulty_min=0.5,
                learning_states=['graduated']
            )
            cards = [self.easy_card, self.new_card, self.hard_card, self.overdue_card]
            
            filtered = _apply_review_filters(cards, filter_config)
            
            # Should include only overdue_card (difficulty 0.6, graduated)
            # Should exclude easy_card (difficulty 0.2), new_card (new state), hard_card (learning state)
            assert len(filtered) == 1
            assert filtered[0].difficulty >= 0.5
            assert getattr(filtered[0], 'learning_state', 'new') == 'graduated'
    
    def test_create_hard_cards_session(self):
        """Test creating a session for hard cards."""
        with patch('srs.models.get_database', return_value=self.db):
            session = create_hard_cards_session('Test Deck')
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # Should include only hard cards (difficulty > 0.7)
            hard_cards = [card for card in session.cards if card.difficulty > 0.7]
            assert len(hard_cards) >= 1  # At least the hard_card
    
    def test_create_easy_cards_session(self):
        """Test creating a session for easy cards."""
        with patch('srs.models.get_database', return_value=self.db):
            session = create_easy_cards_session('Test Deck')
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # Should include only easy cards (difficulty < 0.3)
            easy_cards = [card for card in session.cards if card.difficulty < 0.3]
            assert len(easy_cards) >= 1  # At least the easy_card
    
    def test_create_new_cards_session(self):
        """Test creating a session for new cards."""
        with patch('srs.models.get_database', return_value=self.db):
            session = create_new_cards_session('Test Deck', limit=10)
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # Should include only new cards (repetitions = 0)
            new_cards = [card for card in session.cards if card.repetitions == 0]
            assert len(new_cards) >= 1  # At least the new_card
            assert len(session.cards) <= 10  # Respects limit
    
    def test_create_overdue_cards_session(self):
        """Test creating a session for overdue cards."""
        with patch('srs.models.get_database', return_value=self.db):
            session = create_overdue_cards_session('Test Deck')
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # Should include only overdue cards
            for card in session.cards:
                assert _is_card_overdue(card, datetime.now())
    
    def test_create_learning_cards_session(self):
        """Test creating a session for learning cards."""
        with patch('srs.models.get_database', return_value=self.db):
            session = create_learning_cards_session('Test Deck')
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # Should include only learning cards
            learning_states = ['learning', 'relearning']
            for card in session.cards:
                card_state = getattr(card, 'learning_state', 'new')
                assert card_state in learning_states
    
    def test_create_filtered_review_session(self):
        """Test creating a filtered review session."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(difficulty_min=0.4, difficulty_max=0.7)
            session = create_filtered_review_session('Test Deck', filter_config)
            
            assert session is not None
            assert session.deck.name == 'Test Deck'
            
            # All cards should match the filter
            for card in session.cards:
                assert 0.4 <= card.difficulty <= 0.7
    
    def test_get_card_type(self):
        """Test card type detection."""
        now = datetime.now()
        
        # Test new card
        card_type = _get_card_type(self.new_card, now)
        assert card_type == 'new'
        
        # Test learning card
        card_type = _get_card_type(self.hard_card, now)
        assert card_type == 'learning'
        
        # Test overdue card
        card_type = _get_card_type(self.overdue_card, now)
        assert card_type == 'overdue'
    
    def test_is_card_due(self):
        """Test card due detection."""
        now = datetime.now()
        
        # New cards are always due
        assert _is_card_due(self.new_card, now)
        
        # Card with past due date is due
        assert _is_card_due(self.easy_card, now)
        
        # Card with future due date is not due
        assert not _is_card_due(self.hard_card, now)
    
    def test_is_card_overdue(self):
        """Test card overdue detection."""
        now = datetime.now()
        
        # New cards are never overdue
        assert not _is_card_overdue(self.new_card, now)
        
        # Card overdue by more than 1 day
        assert _is_card_overdue(self.overdue_card, now)
        
        # Card due but not overdue
        assert not _is_card_overdue(self.hard_card, now)
    
    def test_filter_to_search_query(self):
        """Test converting filter to search query."""
        filter_config = ReviewFilter(
            difficulty_min=0.5,
            difficulty_max=0.8,
            interval_min=3,
            learning_states=['graduated']
        )
        
        query = filter_config.to_search_query()
        
        assert 'difficulty:>=0.5' in query
        assert 'difficulty:<=0.8' in query
        assert 'interval:>=3' in query
        assert 'state:graduated' in query
        assert 'AND' in query
    
    def test_nonexistent_deck_filter(self):
        """Test filtering with nonexistent deck."""
        with patch('srs.models.get_database', return_value=self.db):
            filter_config = ReviewFilter(difficulty_min=0.5)
            session = create_filtered_review_session('Nonexistent Deck', filter_config)
            
            assert session is None
    
    def test_no_matching_cards_filter(self):
        """Test filtering that matches no cards."""
        with patch('srs.models.get_database', return_value=self.db):
            # Filter for extremely high difficulty
            filter_config = ReviewFilter(difficulty_min=0.99)
            session = create_filtered_review_session('Test Deck', filter_config)
            
            assert session is None
