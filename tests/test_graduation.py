"""
Tests for the card graduation system.

This module tests the learning process, graduation criteria,
and relearning functionality.
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.graduation import GraduationEngine, LearningResponse
from srs.config import reset_config


class TestGraduationEngine:
    """Test the graduation engine functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_graduation_engine_initialization(self):
        """Test graduation engine initialization."""
        engine = GraduationEngine()
        assert engine.algorithm is not None
    
    def test_learning_response_enum(self):
        """Test LearningResponse enum values."""
        assert LearningResponse.AGAIN.value == 1
        assert LearningResponse.HARD.value == 2
        assert LearningResponse.GOOD.value == 3
        assert LearningResponse.EASY.value == 4


class TestLearningProgression:
    """Test learning progression and state transitions."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_new_card_good_response(self):
        """Test new card with good response."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                card = Card.create(deck.id, 'front', 'back')
                
                # Card should start as new
                assert card.learning_state == 'new'
                assert card.current_step == 0
                
                # Process good response
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                
                # Should move to learning state
                assert card.learning_state == 'learning'
                assert card.current_step == 0
                assert card.step_due_date is not None
                assert result['message'] == 'Started learning'
    
    def test_learning_card_good_response(self):
        """Test learning card advancing through steps."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                deck.learning_steps = "1 10 30"  # 3 steps
                deck.save()
                
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'learning'
                card.current_step = 0
                card.save()
                
                # First good response - advance to step 1
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                assert card.current_step == 1
                assert result['message'] == 'Advanced to next learning step'
                
                # Second good response - advance to step 2
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                assert card.current_step == 2
                assert result['message'] == 'Advanced to next learning step'
                
                # Third good response - should graduate
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                assert card.learning_state == 'graduated'
                assert result['graduated'] is True
                assert result['message'] == 'Card graduated!'
    
    def test_learning_card_again_response(self):
        """Test learning card with again response."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'learning'
                card.current_step = 2
                card.save()
                
                # Process again response
                result = engine.process_learning_response(card, LearningResponse.AGAIN)
                
                # Should restart learning
                assert card.learning_state == 'learning'
                assert card.current_step == 0
                assert result['message'] == 'Learning restarted'
    
    def test_learning_card_hard_response(self):
        """Test learning card with hard response."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'learning'
                card.current_step = 1
                original_step = card.current_step
                card.save()
                
                # Process hard response
                result = engine.process_learning_response(card, LearningResponse.HARD)
                
                # Should repeat current step
                assert card.current_step == original_step
                assert result['message'] == 'Repeating current learning step'
    
    def test_learning_card_easy_response(self):
        """Test learning card with easy response."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                deck.easy_interval = 4
                deck.save()
                
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'learning'
                card.current_step = 0
                card.save()
                
                # Process easy response
                result = engine.process_learning_response(card, LearningResponse.EASY)
                
                # Should graduate with easy interval
                assert card.learning_state == 'graduated'
                assert card.interval == 4
                assert result['graduated'] is True
                assert 'easy interval' in result['message']


class TestRelearning:
    """Test relearning functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_graduated_card_failure(self):
        """Test graduated card failing and entering relearning."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                deck.relearning_steps = "10 30"
                deck.save()
                
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'graduated'
                card.repetitions = 3
                card.save()
                
                # Process again response (failure)
                result = engine.process_learning_response(card, LearningResponse.AGAIN)
                
                # Should enter relearning
                assert card.learning_state == 'relearning'
                assert card.current_step == 0
                assert card.step_due_date is not None
    
    def test_relearning_progression(self):
        """Test progression through relearning steps."""
        with patch('srs.models.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                deck.relearning_steps = "10 30"
                deck.save()
                
                card = Card.create(deck.id, 'front', 'back')
                card.learning_state = 'relearning'
                card.current_step = 0
                card.save()
                
                # First good response in relearning
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                assert card.current_step == 1
                assert card.learning_state == 'relearning'
                
                # Second good response should graduate
                result = engine.process_learning_response(card, LearningResponse.GOOD)
                assert card.learning_state == 'graduated'
                assert result['graduated'] is True


class TestLearningButtons:
    """Test learning button generation."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_learning_buttons_for_new_card(self):
        """Test button generation for new card."""
        with patch('srs.models.get_database', return_value=self.db):
            engine = GraduationEngine()
            deck = Deck.create('Test Deck')
            card = Card.create(deck.id, 'front', 'back')
            
            buttons = engine.get_learning_buttons(card)
            
            assert len(buttons) == 4
            assert buttons[0]['response'] == LearningResponse.AGAIN
            assert buttons[1]['response'] == LearningResponse.HARD
            assert buttons[2]['response'] == LearningResponse.GOOD
            assert buttons[3]['response'] == LearningResponse.EASY
    
    def test_learning_buttons_for_graduated_card(self):
        """Test button generation for graduated card."""
        with patch('srs.models.get_database', return_value=self.db):
            engine = GraduationEngine()
            deck = Deck.create('Test Deck')
            card = Card.create(deck.id, 'front', 'back')
            card.learning_state = 'graduated'
            
            buttons = engine.get_learning_buttons(card)
            
            # Graduated cards should have no learning buttons
            assert len(buttons) == 0


class TestLearningProgress:
    """Test learning progress tracking."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_learning_progress_new_card(self):
        """Test progress tracking for new card."""
        with patch('srs.models.get_database', return_value=self.db):
            engine = GraduationEngine()
            deck = Deck.create('Test Deck')
            deck.learning_steps = "1 10 30"
            deck.save()
            
            card = Card.create(deck.id, 'front', 'back')
            
            progress = engine.get_learning_progress(card)
            
            assert progress['is_learning'] is True
            assert progress['state'] == 'new'
            assert progress['total_steps'] == 3
            assert progress['steps_completed'] == 0
            assert progress['progress'] == 0.0
    
    def test_learning_progress_learning_card(self):
        """Test progress tracking for learning card."""
        with patch('srs.models.get_database', return_value=self.db):
            engine = GraduationEngine()
            deck = Deck.create('Test Deck')
            deck.learning_steps = "1 10 30"
            deck.save()
            
            card = Card.create(deck.id, 'front', 'back')
            card.learning_state = 'learning'
            card.current_step = 1
            
            progress = engine.get_learning_progress(card)
            
            assert progress['is_learning'] is True
            assert progress['state'] == 'learning'
            assert progress['total_steps'] == 3
            assert progress['steps_completed'] == 2  # current_step + 1
            assert progress['progress'] == 2/3
    
    def test_learning_progress_graduated_card(self):
        """Test progress tracking for graduated card."""
        with patch('srs.models.get_database', return_value=self.db):
            engine = GraduationEngine()
            deck = Deck.create('Test Deck')
            card = Card.create(deck.id, 'front', 'back')
            card.learning_state = 'graduated'
            
            progress = engine.get_learning_progress(card)
            
            assert progress['is_learning'] is False
            assert progress['state'] == 'graduated'
            assert progress['progress'] == 1.0


class TestLearningStatistics:
    """Test learning statistics."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_learning_statistics(self):
        """Test learning statistics calculation."""
        with patch('srs.models.get_database', return_value=self.db):
            with patch('srs.database.get_database', return_value=self.db):
                engine = GraduationEngine()
                deck = Deck.create('Test Deck')
                
                # Create cards in different states
                new_card = Card.create(deck.id, 'new', 'card')
                
                learning_card = Card.create(deck.id, 'learning', 'card')
                learning_card.learning_state = 'learning'
                learning_card.save()
                
                graduated_card = Card.create(deck.id, 'graduated', 'card')
                graduated_card.learning_state = 'graduated'
                graduated_card.save()
                
                stats = engine.get_learning_statistics(deck.id)
                
                assert stats['new'] == 1
                assert stats['learning'] == 1
                assert stats['graduated'] == 1
                assert stats['total'] == 3
                assert abs(stats['new_percentage'] - 100/3) < 0.01
                assert abs(stats['learning_percentage'] - 100/3) < 0.01
                assert abs(stats['graduated_percentage'] - 100/3) < 0.01
