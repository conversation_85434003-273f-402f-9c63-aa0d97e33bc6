"""
Tests for the progress tracking and achievement system.

This module tests streak tracking, achievement management, and motivational
features of the progress tracking system.
"""

import pytest
import tempfile
from datetime import datetime, date, timedelta
from unittest.mock import patch

from srs.database import Database, reset_database
from srs.models import Deck, Card, Review
from srs.progress import ProgressTracker, UserStreak, Achievement, UserAchievement
from srs.config import reset_config


class TestProgressTracker:
    """Test the core progress tracker functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_progress_tracker_initialization(self):
        """Test progress tracker initialization."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            assert tracker.db == self.db
            
            # Check that default achievements were created
            results = self.db.execute_query("SELECT COUNT(*) as count FROM achievements")
            assert results[0]['count'] > 0
    
    def test_user_streak_creation(self):
        """Test UserStreak dataclass creation."""
        streak = UserStreak(
            streak_type='daily_review',
            current_streak=5,
            longest_streak=10,
            last_activity_date=date.today()
        )
        
        assert streak.streak_type == 'daily_review'
        assert streak.current_streak == 5
        assert streak.longest_streak == 10
        assert streak.user_id == 'default'
        assert streak.last_activity_date == date.today()
    
    def test_achievement_creation(self):
        """Test Achievement dataclass creation."""
        achievement = Achievement(
            name='Test Achievement',
            description='Test description',
            criteria={'reviews': 10},
            icon='🎯'
        )
        
        assert achievement.name == 'Test Achievement'
        assert achievement.description == 'Test description'
        assert achievement.criteria == {'reviews': 10}
        assert achievement.icon == '🎯'


class TestStreakTracking:
    """Test streak tracking functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_create_new_streak(self):
        """Test creating a new streak."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Update streak for first time
            tracker.update_streak('daily_review')
            
            # Check streak was created
            streak = tracker.get_user_streak('daily_review')
            assert streak is not None
            assert streak.current_streak == 1
            assert streak.longest_streak == 1
            assert streak.last_activity_date == date.today()
    
    def test_consecutive_day_streak(self):
        """Test consecutive day streak increment."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Day 1
            yesterday = date.today() - timedelta(days=1)
            tracker.update_streak('daily_review', yesterday)
            
            # Day 2 (today)
            tracker.update_streak('daily_review')
            
            # Check streak incremented
            streak = tracker.get_user_streak('daily_review')
            assert streak.current_streak == 2
            assert streak.longest_streak == 2
    
    def test_same_day_streak_no_change(self):
        """Test that same day activity doesn't change streak."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # First activity today
            tracker.update_streak('daily_review')
            
            # Second activity same day
            tracker.update_streak('daily_review')
            
            # Check streak didn't change
            streak = tracker.get_user_streak('daily_review')
            assert streak.current_streak == 1
            assert streak.longest_streak == 1
    
    def test_streak_reset_after_gap(self):
        """Test streak reset after gap in activity."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Day 1
            three_days_ago = date.today() - timedelta(days=3)
            tracker.update_streak('daily_review', three_days_ago)
            
            # Day 2
            two_days_ago = date.today() - timedelta(days=2)
            tracker.update_streak('daily_review', two_days_ago)
            
            # Skip a day, then activity today
            tracker.update_streak('daily_review')
            
            # Check streak was reset but longest streak preserved
            streak = tracker.get_user_streak('daily_review')
            assert streak.current_streak == 1
            assert streak.longest_streak == 2
    
    def test_longest_streak_tracking(self):
        """Test longest streak tracking."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Build up a 5-day streak
            for i in range(5):
                activity_date = date.today() - timedelta(days=4-i)
                tracker.update_streak('daily_review', activity_date)
            
            streak = tracker.get_user_streak('daily_review')
            assert streak.current_streak == 5
            assert streak.longest_streak == 5
            
            # Reset streak with gap
            future_date = date.today() + timedelta(days=2)
            tracker.update_streak('daily_review', future_date)
            
            # Build new shorter streak
            next_day = future_date + timedelta(days=1)
            tracker.update_streak('daily_review', next_day)
            
            streak = tracker.get_user_streak('daily_review')
            assert streak.current_streak == 2
            assert streak.longest_streak == 5  # Preserved


class TestAchievementSystem:
    """Test achievement system functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_create_achievement(self):
        """Test creating a custom achievement."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            achievement_id = tracker.create_achievement(
                name='Test Achievement',
                description='Test description',
                criteria={'test': True},
                icon='🧪'
            )
            
            assert achievement_id > 0
            
            # Verify achievement was created
            results = self.db.execute_query(
                "SELECT * FROM achievements WHERE id = ?", (achievement_id,)
            )
            assert len(results) == 1
            assert results[0]['name'] == 'Test Achievement'
    
    def test_unlock_achievement(self):
        """Test unlocking an achievement."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Should have default achievements
            achievements = tracker.get_user_achievements()
            initial_count = len(achievements)
            
            # Unlock an achievement
            tracker._unlock_achievement_if_not_unlocked('default', 'First Steps')
            
            # Check achievement was unlocked
            achievements = tracker.get_user_achievements()
            assert len(achievements) == initial_count + 1
            assert any(a['name'] == 'First Steps' for a in achievements)
    
    def test_prevent_duplicate_unlock(self):
        """Test that achievements can't be unlocked twice."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Unlock achievement twice
            tracker._unlock_achievement_if_not_unlocked('default', 'First Steps')
            tracker._unlock_achievement_if_not_unlocked('default', 'First Steps')
            
            # Should only have one unlock
            achievements = tracker.get_user_achievements()
            first_steps_count = sum(1 for a in achievements if a['name'] == 'First Steps')
            assert first_steps_count == 1
    
    def test_review_based_achievements(self):
        """Test achievements based on review counts."""
        with patch('srs.progress.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                tracker = ProgressTracker()
                
                # Create test data
                deck = Deck.create('Test Deck')
                card = Card.create(deck.id, 'front', 'back')
                
                # Create a review
                Review.create(card.id, 4)
                
                # Check achievements
                tracker.check_review_achievements()
                
                # Should unlock "First Steps"
                achievements = tracker.get_user_achievements()
                assert any(a['name'] == 'First Steps' for a in achievements)
    
    def test_session_based_achievements(self):
        """Test achievements based on session performance."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Test perfect session achievement
            session_stats = {
                'cards_reviewed': 5,
                'rating_counts': {3: 3, 4: 2}  # All good ratings
            }
            
            tracker.check_session_achievements(session_stats)
            
            # Should unlock "Perfect Session"
            achievements = tracker.get_user_achievements()
            assert any(a['name'] == 'Perfect Session' for a in achievements)
    
    def test_streak_achievements(self):
        """Test achievements based on streaks."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Build up a 7-day streak
            for i in range(7):
                activity_date = date.today() - timedelta(days=6-i)
                tracker.update_streak('daily_review', activity_date)
            
            # Should unlock "Study Streak"
            achievements = tracker.get_user_achievements()
            assert any(a['name'] == 'Study Streak' for a in achievements)


class TestProgressSummary:
    """Test progress summary functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_progress_summary_empty(self):
        """Test progress summary with no activity."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            summary = tracker.get_progress_summary()
            
            assert summary['daily_streak'] == 0
            assert summary['longest_daily_streak'] == 0
            assert summary['achievements_unlocked'] == 0
            assert summary['total_achievements'] > 0  # Default achievements exist
            assert summary['achievement_percentage'] == 0
    
    def test_progress_summary_with_activity(self):
        """Test progress summary with some activity."""
        with patch('srs.progress.get_database', return_value=self.db):
            tracker = ProgressTracker()
            
            # Create some activity
            tracker.update_streak('daily_review')
            tracker._unlock_achievement_if_not_unlocked('default', 'First Steps')
            
            summary = tracker.get_progress_summary()
            
            assert summary['daily_streak'] == 1
            assert summary['longest_daily_streak'] == 1
            assert summary['achievements_unlocked'] == 1
            assert summary['achievement_percentage'] > 0
            assert len(summary['recent_achievements']) == 1


class TestProgressDatabaseMigration:
    """Test progress tracking database migration."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_schema_version_6(self):
        """Test that schema version 6 is applied."""
        # Check schema version (should be 6 or higher due to later migrations)
        result = self.db.execute_query("SELECT MAX(version) as version FROM schema_version")
        assert result[0]['version'] >= 6
    
    def test_progress_tables_exist(self):
        """Test that progress tracking tables were created."""
        # Check user_streaks table
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='user_streaks'"
        )
        assert len(result) == 1
        
        # Check achievements table
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='achievements'"
        )
        assert len(result) == 1
        
        # Check user_achievements table
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='user_achievements'"
        )
        assert len(result) == 1
    
    def test_progress_indexes_created(self):
        """Test that progress tracking indexes were created."""
        # Get all indexes
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index'"
        )
        index_names = [row['name'] for row in result]
        
        # Check for progress-related indexes
        assert any('user_streaks' in name for name in index_names)
        assert any('user_achievements' in name for name in index_names)
