"""
Tests for the smart scheduling system.

This module tests the smart scheduler, session limits, card prioritization,
and time-boxed session functionality.
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.scheduling import SmartScheduler, SessionLimits, CardPriority, CardScore
from srs.review import create_smart_review_session, create_time_boxed_session
from srs.config import reset_config


class TestSmartScheduler:
    """Test the smart scheduler functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_scheduler_initialization(self):
        """Test smart scheduler initialization."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            scheduler = SmartScheduler()
            assert scheduler.db == self.db
    
    def test_session_limits_creation(self):
        """Test SessionLimits dataclass creation."""
        limits = SessionLimits(
            max_cards=50,
            max_new_cards=10,
            max_review_cards=40,
            max_time_minutes=30,
            new_cards_only=True
        )
        
        assert limits.max_cards == 50
        assert limits.max_new_cards == 10
        assert limits.max_review_cards == 40
        assert limits.max_time_minutes == 30
        assert limits.new_cards_only is True
        assert limits.due_cards_only is False
    
    def test_card_score_creation(self):
        """Test CardScore dataclass creation."""
        with patch('srs.models.get_database', return_value=self.db):
            deck = Deck.create('Test Deck')
            card = Card.create(deck.id, 'front', 'back')
            
            score = CardScore(
                card=card,
                priority=CardPriority.DUE_TODAY,
                days_overdue=1.5,
                difficulty_score=3.2,
                total_score=150.0
            )
            
            assert score.card == card
            assert score.priority == CardPriority.DUE_TODAY
            assert score.days_overdue == 1.5
            assert score.difficulty_score == 3.2
            assert score.total_score == 150.0


class TestCardPrioritization:
    """Test card prioritization and scoring."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_score_new_cards(self):
        """Test scoring of new cards."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create new card
                card = Card.create(deck.id, 'front', 'back')
                card.repetitions = 0
                card.difficulty = 0.8
                
                scored_cards = scheduler._score_cards([card])
                
                assert len(scored_cards) == 1
                scored = scored_cards[0]
                assert scored.priority == CardPriority.NEW
                assert scored.days_overdue == 0.0
                assert scored.difficulty_score == 0.8
    
    def test_score_overdue_cards(self):
        """Test scoring of overdue cards."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create overdue card
                card = Card.create(deck.id, 'front', 'back')
                card.repetitions = 3
                card.due_date = datetime.now() - timedelta(days=2)
                card.difficulty = 0.6
                
                scored_cards = scheduler._score_cards([card])
                
                assert len(scored_cards) == 1
                scored = scored_cards[0]
                assert scored.priority == CardPriority.OVERDUE
                assert scored.days_overdue > 1.0
                assert scored.difficulty_score == 0.6
    
    def test_score_due_today_cards(self):
        """Test scoring of cards due today."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create card due today
                card = Card.create(deck.id, 'front', 'back')
                card.repetitions = 2
                card.due_date = datetime.now() - timedelta(hours=2)
                card.difficulty = 0.9
                
                scored_cards = scheduler._score_cards([card])
                
                assert len(scored_cards) == 1
                scored = scored_cards[0]
                assert scored.priority == CardPriority.DUE_TODAY
                assert 0.0 <= scored.days_overdue < 1.0
                assert scored.difficulty_score == 0.9
    
    def test_sort_cards_by_priority(self):
        """Test sorting cards by priority."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create cards with different priorities
                new_card = Card.create(deck.id, 'new', 'card')
                new_card.repetitions = 0
                
                due_card = Card.create(deck.id, 'due', 'card')
                due_card.repetitions = 1
                due_card.due_date = datetime.now() - timedelta(hours=1)
                
                overdue_card = Card.create(deck.id, 'overdue', 'card')
                overdue_card.repetitions = 2
                overdue_card.due_date = datetime.now() - timedelta(days=2)
                
                cards = [new_card, due_card, overdue_card]
                scored_cards = scheduler._score_cards(cards)
                sorted_cards = scheduler._sort_cards_by_priority(scored_cards)
                
                # Overdue should come first, then due, then new
                assert sorted_cards[0].front == 'overdue'
                assert sorted_cards[1].front == 'due'
                assert sorted_cards[2].front == 'new'


class TestSessionLimits:
    """Test session limit enforcement."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_apply_max_cards_limit(self):
        """Test applying maximum cards limit."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create more cards than limit
                cards = []
                for i in range(10):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    cards.append(card)
                
                limits = SessionLimits(max_cards=5)
                limited_cards = scheduler._apply_session_limits(cards, limits)
                
                assert len(limited_cards) == 5
    
    def test_apply_new_cards_limit(self):
        """Test applying new cards limit."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create mix of new and review cards
                cards = []
                for i in range(5):
                    # New cards
                    new_card = Card.create(deck.id, f'new{i}', f'back{i}')
                    new_card.repetitions = 0
                    cards.append(new_card)
                    
                    # Review cards
                    review_card = Card.create(deck.id, f'review{i}', f'back{i}')
                    review_card.repetitions = 1
                    cards.append(review_card)
                
                limits = SessionLimits(max_new_cards=2)
                limited_cards = scheduler._apply_session_limits(cards, limits)
                
                new_count = sum(1 for card in limited_cards if card.repetitions == 0)
                assert new_count <= 2
    
    def test_apply_review_cards_limit(self):
        """Test applying review cards limit."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create mix of new and review cards
                cards = []
                for i in range(5):
                    # New cards
                    new_card = Card.create(deck.id, f'new{i}', f'back{i}')
                    new_card.repetitions = 0
                    cards.append(new_card)
                    
                    # Review cards
                    review_card = Card.create(deck.id, f'review{i}', f'back{i}')
                    review_card.repetitions = 1
                    cards.append(review_card)
                
                limits = SessionLimits(max_review_cards=2)
                limited_cards = scheduler._apply_session_limits(cards, limits)
                
                review_count = sum(1 for card in limited_cards if card.repetitions > 0)
                assert review_count <= 2


class TestSmartSession:
    """Test smart session creation."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_create_smart_session(self):
        """Test creating a smart session."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create test cards
                for i in range(5):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    if i < 2:
                        card.repetitions = 0  # New cards
                    else:
                        card.repetitions = 1  # Review cards
                        card.due_date = datetime.now() - timedelta(hours=1)
                    card.save()
                
                limits = SessionLimits(max_cards=3)
                cards = scheduler.create_smart_session(deck, limits)
                
                assert len(cards) <= 3
                assert all(isinstance(card, Card) for card in cards)
    
    def test_create_new_cards_only_session(self):
        """Test creating a session with new cards only."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create mix of new and review cards
                for i in range(5):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    if i < 3:
                        card.repetitions = 0  # New cards
                    else:
                        card.repetitions = 1  # Review cards
                        card.due_date = datetime.now() - timedelta(hours=1)
                    card.save()
                
                limits = SessionLimits(new_cards_only=True)
                cards = scheduler.create_smart_session(deck, limits)
                
                assert all(card.repetitions == 0 for card in cards)
                assert len(cards) == 3  # Only the new cards
    
    def test_create_due_cards_only_session(self):
        """Test creating a session with due cards only."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create mix of new and review cards
                for i in range(5):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    if i < 2:
                        card.repetitions = 0  # New cards
                    else:
                        card.repetitions = 1  # Review cards
                        card.due_date = datetime.now() - timedelta(hours=1)
                    card.save()
                
                limits = SessionLimits(due_cards_only=True)
                cards = scheduler.create_smart_session(deck, limits)
                
                assert all(card.repetitions > 0 for card in cards)
                assert len(cards) == 3  # Only the review cards


class TestTimeBoxedSessions:
    """Test time-boxed session functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_estimate_session_time(self):
        """Test session time estimation."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create cards with different types
                cards = []
                
                # New card (30 seconds base)
                new_card = Card.create(deck.id, 'new', 'card')
                new_card.repetitions = 0
                new_card.difficulty = 0.5
                cards.append(new_card)

                # Review card (15 seconds base)
                review_card = Card.create(deck.id, 'review', 'card')
                review_card.repetitions = 1
                review_card.difficulty = 0.5
                cards.append(review_card)
                
                estimated_time = scheduler.estimate_session_time(cards)
                
                # Should be at least 1 minute (30 + 15 seconds = 45 seconds, rounded up)
                assert estimated_time >= 1
    
    def test_create_time_boxed_session(self):
        """Test creating a time-boxed session."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create many cards
                for i in range(20):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    card.repetitions = 0  # New cards take more time
                    card.difficulty = 0.5
                    card.save()
                
                # Create a 2-minute time-boxed session
                cards = scheduler.create_time_boxed_session(deck, 2)
                
                # Should select fewer cards to fit time limit
                assert len(cards) < 20
                assert len(cards) > 0
    
    def test_get_session_statistics(self):
        """Test getting session statistics."""
        with patch('srs.scheduling.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                scheduler = SmartScheduler()
                deck = Deck.create('Test Deck')
                
                # Create mix of cards
                cards = []
                for i in range(5):
                    card = Card.create(deck.id, f'front{i}', f'back{i}')
                    if i < 2:
                        card.repetitions = 0  # New cards
                        card.difficulty = 0.6
                    else:
                        card.repetitions = 1  # Review cards
                        card.due_date = datetime.now() - timedelta(hours=1)
                        card.difficulty = 0.8
                    cards.append(card)
                
                stats = scheduler.get_session_statistics(cards)
                
                assert stats['total_cards'] == 5
                assert stats['new_cards'] == 2
                assert stats['review_cards'] == 3
                assert stats['estimated_time_minutes'] > 0
                assert stats['average_difficulty'] > 0
