"""
Tests for the statistics system.

This module tests the statistics engine, daily/deck statistics tracking,
retention rate calculations, and performance metrics.
"""

import pytest
import tempfile
from datetime import datetime, date, timedelta
from unittest.mock import patch

from srs.database import Database, reset_database
from srs.models import Deck, Card, Review
from srs.statistics import StatisticsEngine, DailyStats, DeckStats
from srs.config import reset_config


class TestStatisticsEngine:
    """Test the core statistics engine functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_statistics_engine_initialization(self):
        """Test statistics engine initialization."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()
            assert engine.db == self.db
    
    def test_daily_stats_creation(self):
        """Test DailyStats dataclass creation."""
        stats = DailyStats(
            cards_reviewed=10,
            new_cards_learned=5,
            review_time_seconds=300,
            average_ease_factor=2.5,
            retention_rate=85.0
        )
        
        assert stats.cards_reviewed == 10
        assert stats.new_cards_learned == 5
        assert stats.review_time_seconds == 300
        assert stats.average_ease_factor == 2.5
        assert stats.retention_rate == 85.0
        assert stats.user_id == 'default'
        assert stats.date == date.today()
    
    def test_deck_stats_creation(self):
        """Test DeckStats dataclass creation."""
        stats = DeckStats(
            deck_id=1,
            cards_reviewed=8,
            new_cards_learned=3,
            average_rating=3.2,
            retention_rate=90.0
        )
        
        assert stats.deck_id == 1
        assert stats.cards_reviewed == 8
        assert stats.new_cards_learned == 3
        assert stats.average_rating == 3.2
        assert stats.retention_rate == 90.0
        assert stats.date == date.today()


class TestStatisticsDatabaseOperations:
    """Test statistics database operations."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_record_daily_stats(self):
        """Test recording daily statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()
            
            stats = DailyStats(
                cards_reviewed=15,
                new_cards_learned=7,
                review_time_seconds=450,
                average_ease_factor=2.3,
                retention_rate=88.5
            )
            
            stats_id = engine.record_daily_stats(stats)
            assert stats_id > 0
            
            # Verify the record was created
            results = self.db.execute_query(
                "SELECT * FROM daily_stats WHERE id = ?", (stats_id,)
            )
            assert len(results) == 1
            assert results[0]['cards_reviewed'] == 15
            assert results[0]['new_cards_learned'] == 7
    
    def test_record_deck_stats(self):
        """Test recording deck statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create a deck first
                deck = Deck.create('Test Deck')
                
                engine = StatisticsEngine()
                
                stats = DeckStats(
                    deck_id=deck.id,
                    cards_reviewed=12,
                    new_cards_learned=4,
                    average_rating=3.5,
                    retention_rate=92.0
                )
                
                stats_id = engine.record_deck_stats(stats)
                assert stats_id > 0
                
                # Verify the record was created
                results = self.db.execute_query(
                    "SELECT * FROM deck_stats WHERE id = ?", (stats_id,)
                )
                assert len(results) == 1
                assert results[0]['deck_id'] == deck.id
                assert results[0]['cards_reviewed'] == 12
    
    def test_get_daily_stats(self):
        """Test retrieving daily statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()
            
            # Create test data for multiple days
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            stats1 = DailyStats(date=yesterday, cards_reviewed=10)
            stats2 = DailyStats(date=today, cards_reviewed=15)
            
            engine.record_daily_stats(stats1)
            engine.record_daily_stats(stats2)
            
            # Retrieve stats for date range
            retrieved_stats = engine.get_daily_stats(yesterday, today)
            
            assert len(retrieved_stats) == 2
            assert retrieved_stats[0].date == yesterday
            assert retrieved_stats[0].cards_reviewed == 10
            assert retrieved_stats[1].date == today
            assert retrieved_stats[1].cards_reviewed == 15
    
    def test_get_deck_stats(self):
        """Test retrieving deck statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create a deck first
                deck = Deck.create('Test Deck')
                
                engine = StatisticsEngine()
                
                # Create test data for multiple days
                today = date.today()
                yesterday = today - timedelta(days=1)
                
                stats1 = DeckStats(deck_id=deck.id, date=yesterday, cards_reviewed=8)
                stats2 = DeckStats(deck_id=deck.id, date=today, cards_reviewed=12)
                
                engine.record_deck_stats(stats1)
                engine.record_deck_stats(stats2)
                
                # Retrieve stats for date range
                retrieved_stats = engine.get_deck_stats(deck.id, yesterday, today)
                
                assert len(retrieved_stats) == 2
                assert retrieved_stats[0].date == yesterday
                assert retrieved_stats[0].cards_reviewed == 8
                assert retrieved_stats[1].date == today
                assert retrieved_stats[1].cards_reviewed == 12


class TestRetentionRateCalculation:
    """Test retention rate calculation functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_calculate_retention_rate_basic(self):
        """Test basic retention rate calculation."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create test data
                deck = Deck.create('Test Deck')
                card1 = Card.create(deck.id, 'front1', 'back1')
                card2 = Card.create(deck.id, 'front2', 'back2')
                card3 = Card.create(deck.id, 'front3', 'back3')
                
                # Create reviews with different ratings
                Review.create(card1.id, 4)  # Success
                Review.create(card2.id, 3)  # Success
                Review.create(card3.id, 2)  # Failure
                
                engine = StatisticsEngine()
                
                # Calculate retention rate
                today = date.today()
                retention_rate = engine.calculate_retention_rate(today, today)
                
                # Should be 66.67% (2 out of 3 successful)
                assert abs(retention_rate - 66.67) < 0.1
    
    def test_calculate_retention_rate_no_reviews(self):
        """Test retention rate calculation with no reviews."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()
            
            today = date.today()
            retention_rate = engine.calculate_retention_rate(today, today)
            
            assert retention_rate == 0.0
    
    def test_calculate_retention_rate_deck_specific(self):
        """Test retention rate calculation for specific deck."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create test data with multiple decks
                deck1 = Deck.create('Deck 1')
                deck2 = Deck.create('Deck 2')
                
                card1 = Card.create(deck1.id, 'front1', 'back1')
                card2 = Card.create(deck1.id, 'front2', 'back2')
                card3 = Card.create(deck2.id, 'front3', 'back3')
                
                # Create reviews
                Review.create(card1.id, 4)  # Deck 1 - Success
                Review.create(card2.id, 2)  # Deck 1 - Failure
                Review.create(card3.id, 4)  # Deck 2 - Success
                
                engine = StatisticsEngine()
                
                today = date.today()
                
                # Test deck-specific retention rate
                deck1_retention = engine.calculate_retention_rate(today, today, deck1.id)
                deck2_retention = engine.calculate_retention_rate(today, today, deck2.id)
                
                assert abs(deck1_retention - 50.0) < 0.1  # 1 out of 2
                assert abs(deck2_retention - 100.0) < 0.1  # 1 out of 1


class TestStatisticsUpdates:
    """Test automatic statistics updates."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_update_daily_statistics(self):
        """Test updating daily statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create test data
                deck = Deck.create('Test Deck')
                card1 = Card.create(deck.id, 'front1', 'back1')
                card2 = Card.create(deck.id, 'front2', 'back2')
                
                # Create reviews
                Review.create(card1.id, 4, response_time=2000)
                Review.create(card2.id, 3, response_time=1500)
                
                engine = StatisticsEngine()
                
                # Update daily statistics
                today = date.today()
                engine.update_daily_statistics(today)
                
                # Verify statistics were recorded
                daily_stats = engine.get_daily_stats(today, today)
                assert len(daily_stats) == 1
                
                stats = daily_stats[0]
                assert stats.cards_reviewed == 2
                assert stats.review_time_seconds == 3  # 3500ms = 3.5s rounded down
    
    def test_update_deck_statistics(self):
        """Test updating deck-specific statistics."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create test data
                deck = Deck.create('Test Deck')
                card1 = Card.create(deck.id, 'front1', 'back1')
                card2 = Card.create(deck.id, 'front2', 'back2')
                
                # Create reviews
                Review.create(card1.id, 4)
                Review.create(card2.id, 3)
                
                engine = StatisticsEngine()
                
                # Update deck statistics
                today = date.today()
                engine.update_deck_statistics(deck.id, today)
                
                # Verify statistics were recorded
                deck_stats = engine.get_deck_stats(deck.id, today, today)
                assert len(deck_stats) == 1
                
                stats = deck_stats[0]
                assert stats.cards_reviewed == 2
                assert stats.average_rating == 3.5  # (4 + 3) / 2


class TestPerformanceSummary:
    """Test performance summary functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_get_performance_summary_empty(self):
        """Test performance summary with no data."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()

            summary = engine.get_performance_summary(days=7)

            assert summary['period_days'] == 7
            assert summary['total_cards_reviewed'] == 0
            assert summary['total_new_cards'] == 0
            assert summary['total_study_time'] == 0
            assert summary['average_retention_rate'] == 0.0
            assert summary['study_streak'] == 0
            assert summary['daily_average'] == 0.0

    def test_get_performance_summary_with_data(self):
        """Test performance summary with actual data."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()

            # Create test daily statistics
            today = date.today()
            yesterday = today - timedelta(days=1)

            stats1 = DailyStats(
                date=yesterday,
                cards_reviewed=10,
                new_cards_learned=5,
                review_time_seconds=300,
                retention_rate=85.0
            )
            stats2 = DailyStats(
                date=today,
                cards_reviewed=15,
                new_cards_learned=3,
                review_time_seconds=450,
                retention_rate=90.0
            )

            engine.record_daily_stats(stats1)
            engine.record_daily_stats(stats2)

            summary = engine.get_performance_summary(days=2)

            assert summary['period_days'] == 2
            assert summary['total_cards_reviewed'] == 25
            assert summary['total_new_cards'] == 8
            assert summary['total_study_time'] == 750
            assert summary['average_retention_rate'] == 87.5  # (85 + 90) / 2
            assert summary['study_streak'] == 2
            assert summary['daily_average'] == 12.5  # 25 / 2
            assert summary['active_days'] == 2

    def test_calculate_study_streak(self):
        """Test study streak calculation."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()

            # Create test data with gaps
            today = date.today()
            dates = [today - timedelta(days=i) for i in range(5, -1, -1)]

            daily_stats = [
                DailyStats(date=dates[0], cards_reviewed=0),  # 5 days ago - no activity
                DailyStats(date=dates[1], cards_reviewed=5),  # 4 days ago - activity
                DailyStats(date=dates[2], cards_reviewed=0),  # 3 days ago - no activity
                DailyStats(date=dates[3], cards_reviewed=8),  # 2 days ago - activity
                DailyStats(date=dates[4], cards_reviewed=12), # yesterday - activity
                DailyStats(date=dates[5], cards_reviewed=10), # today - activity
            ]

            streak = engine._calculate_study_streak(daily_stats)
            assert streak == 3  # Last 3 days have activity


class TestStatisticsEdgeCases:
    """Test edge cases and error handling."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_calculate_average_ease_factor_no_data(self):
        """Test average ease factor calculation with no data."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()

            today = date.today()
            avg_ease = engine.calculate_average_ease_factor(today, today)

            assert avg_ease == 0.0

    def test_calculate_average_ease_factor_with_data(self):
        """Test average ease factor calculation with data."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                # Create test data
                deck = Deck.create('Test Deck')
                card1 = Card.create(deck.id, 'front1', 'back1')
                card2 = Card.create(deck.id, 'front2', 'back2')

                # Set different ease factors
                card1.ease_factor = 2.5
                card1.save()
                card2.ease_factor = 2.3
                card2.save()

                # Create reviews
                Review.create(card1.id, 4)
                Review.create(card2.id, 3)

                engine = StatisticsEngine()

                today = date.today()
                avg_ease = engine.calculate_average_ease_factor(today, today)

                assert abs(avg_ease - 2.4) < 0.01  # (2.5 + 2.3) / 2

    def test_daily_stats_replace_existing(self):
        """Test that daily stats replace existing records for same date."""
        with patch('srs.statistics.get_database', return_value=self.db):
            engine = StatisticsEngine()

            today = date.today()

            # Record initial stats
            stats1 = DailyStats(date=today, cards_reviewed=10)
            engine.record_daily_stats(stats1)

            # Record updated stats for same date
            stats2 = DailyStats(date=today, cards_reviewed=15)
            engine.record_daily_stats(stats2)

            # Should only have one record with updated values
            retrieved_stats = engine.get_daily_stats(today, today)
            assert len(retrieved_stats) == 1
            assert retrieved_stats[0].cards_reviewed == 15

    def test_deck_stats_replace_existing(self):
        """Test that deck stats replace existing records for same date."""
        with patch('srs.statistics.get_database', return_value=self.db):
            with patch('srs.models.get_database', return_value=self.db):
                deck = Deck.create('Test Deck')
                engine = StatisticsEngine()

                today = date.today()

                # Record initial stats
                stats1 = DeckStats(deck_id=deck.id, date=today, cards_reviewed=8)
                engine.record_deck_stats(stats1)

                # Record updated stats for same date
                stats2 = DeckStats(deck_id=deck.id, date=today, cards_reviewed=12)
                engine.record_deck_stats(stats2)

                # Should only have one record with updated values
                retrieved_stats = engine.get_deck_stats(deck.id, today, today)
                assert len(retrieved_stats) == 1
                assert retrieved_stats[0].cards_reviewed == 12


class TestStatisticsDatabaseMigration:
    """Test statistics database migration."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_schema_version_5(self):
        """Test that schema version 5 is applied."""
        # Check schema version (should be at least 5, could be higher with newer migrations)
        result = self.db.execute_query("SELECT MAX(version) as version FROM schema_version")
        assert result[0]['version'] >= 5

    def test_statistics_tables_exist(self):
        """Test that statistics tables were created."""
        # Check daily_stats table
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='daily_stats'"
        )
        assert len(result) == 1

        # Check deck_stats table
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='deck_stats'"
        )
        assert len(result) == 1

    def test_reviews_table_enhanced(self):
        """Test that reviews table has new columns."""
        # Check table structure
        result = self.db.execute_query("PRAGMA table_info(reviews)")
        column_names = [row['name'] for row in result]

        assert 'response_time' in column_names
        assert 'session_id' in column_names
        assert 'review_type' in column_names

    def test_statistics_indexes_created(self):
        """Test that statistics indexes were created."""
        # Get all indexes
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index'"
        )
        index_names = [row['name'] for row in result]

        # Check for statistics-related indexes
        assert any('daily_stats' in name for name in index_names)
        assert any('deck_stats' in name for name in index_names)
        assert any('session_id' in name for name in index_names)
