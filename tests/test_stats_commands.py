"""
Tests for the statistics CLI commands.

This module tests the stats command functionality including general statistics,
retention analysis, performance trends, and export capabilities.
"""

import pytest
import tempfile
import json
import csv
from datetime import datetime, date, timedelta
from unittest.mock import patch, MagicMock
from pathlib import Path

from srs.database import Database, reset_database
from srs.models import Deck, Card, Review
from srs.cli import cli
from srs.config import reset_config


class TestStatsCommand:
    """Test the stats command functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_stats_command_basic(self):
        """Test basic stats command."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test stats command
                            exit_code = cli(['stats'])
                            assert exit_code == 0
    
    def test_stats_command_specific_deck(self):
        """Test stats command for specific deck."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test stats command for specific deck
                            exit_code = cli(['stats', 'Test Deck'])
                            assert exit_code == 0
    
    def test_stats_command_nonexistent_deck(self):
        """Test stats command for nonexistent deck."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Test stats command for nonexistent deck
                            exit_code = cli(['stats', 'Nonexistent Deck'])
                            assert exit_code == 1
    
    def test_stats_command_different_periods(self):
        """Test stats command with different time periods."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test different periods
                            for period in ['week', 'month', 'year', 'all']:
                                exit_code = cli(['stats', '--period', period])
                                assert exit_code == 0
    
    def test_stats_retention_analysis(self):
        """Test stats command with retention analysis."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card1 = Card.create(deck.id, 'front1', 'back1')
                            card2 = Card.create(deck.id, 'front2', 'back2')
                            
                            # Create reviews with different ratings
                            Review.create(card1.id, 4)  # Success
                            Review.create(card2.id, 2)  # Failure
                            
                            # Test retention analysis
                            exit_code = cli(['stats', '--retention'])
                            assert exit_code == 0
    
    def test_stats_forecast(self):
        """Test stats command with review forecast."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            
                            # Set card due in the future
                            card.due_date = datetime.now() + timedelta(days=2)
                            card.save()
                            
                            # Test forecast
                            exit_code = cli(['stats', '--forecast'])
                            assert exit_code == 0
    
    def test_stats_performance_trends(self):
        """Test stats command with performance trends."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test performance trends
                            exit_code = cli(['stats', '--performance'])
                            assert exit_code == 0


class TestStatsExport:
    """Test statistics export functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_stats_export_json(self):
        """Test exporting statistics to JSON."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test JSON export
                            output_file = Path(self.temp_dir) / 'test_stats.json'
                            exit_code = cli(['stats', '--export', 'json', '--output', str(output_file)])
                            assert exit_code == 0
                            
                            # Verify file was created and contains valid JSON
                            assert output_file.exists()
                            with open(output_file, 'r') as f:
                                data = json.load(f)
                                assert 'generated_at' in data
                                assert 'summary' in data
                                assert 'daily_statistics' in data
    
    def test_stats_export_csv(self):
        """Test exporting statistics to CSV."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test CSV export
                            output_file = Path(self.temp_dir) / 'test_stats.csv'
                            exit_code = cli(['stats', '--export', 'csv', '--output', str(output_file)])
                            assert exit_code == 0
                            
                            # Verify file was created and contains valid CSV
                            assert output_file.exists()
                            with open(output_file, 'r') as f:
                                reader = csv.reader(f)
                                rows = list(reader)
                                assert len(rows) >= 1  # At least header row
    
    def test_stats_export_auto_filename(self):
        """Test exporting statistics with auto-generated filename."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                with patch('srs.models.get_database', return_value=self.db):
                    with patch('srs.statistics.get_database', return_value=self.db):
                        with patch('srs.progress.get_database', return_value=self.db):
                            # Create test data
                            deck = Deck.create('Test Deck')
                            card = Card.create(deck.id, 'front', 'back')
                            Review.create(card.id, 4)
                            
                            # Test export with auto filename
                            import os
                            original_cwd = os.getcwd()
                            try:
                                os.chdir(self.temp_dir)
                                exit_code = cli(['stats', '--export', 'json'])
                                assert exit_code == 0

                                # Check that a file was created
                                json_files = list(Path(self.temp_dir).glob('srs_stats_*.json'))
                                assert len(json_files) >= 1
                            finally:
                                os.chdir(original_cwd)


class TestVisualizationFunctions:
    """Test visualization utility functions."""
    
    def test_progress_bar_creation(self):
        """Test progress bar creation."""
        from srs.visualization import create_progress_bar
        
        # Test normal progress
        bar = create_progress_bar(50, 100)
        assert '50.0%' in bar
        assert '(50/100)' in bar
        
        # Test edge cases
        bar_empty = create_progress_bar(0, 100)
        assert '0.0%' in bar_empty
        
        bar_full = create_progress_bar(100, 100)
        assert '100.0%' in bar_full
        
        bar_zero_total = create_progress_bar(0, 0)
        assert '0.0%' in bar_zero_total
    
    def test_horizontal_bar_chart(self):
        """Test horizontal bar chart creation."""
        from srs.visualization import create_horizontal_bar_chart
        
        data = {'A': 10, 'B': 20, 'C': 15}
        chart = create_horizontal_bar_chart(data, title="Test Chart")
        
        assert 'Test Chart' in chart
        assert 'A' in chart
        assert 'B' in chart
        assert 'C' in chart
        assert '10' in chart
        assert '20' in chart
        assert '15' in chart
    
    def test_line_chart_creation(self):
        """Test line chart creation."""
        from srs.visualization import create_line_chart
        
        data = [('Day 1', 10), ('Day 2', 15), ('Day 3', 12)]
        chart = create_line_chart(data, title="Test Line Chart")
        
        assert 'Test Line Chart' in chart
        assert '●' in chart  # Data points
    
    def test_summary_box_creation(self):
        """Test summary box creation."""
        from srs.visualization import create_summary_box
        
        stats = {'Cards': 100, 'Time': '30m', 'Rate': '85%'}
        box = create_summary_box("Test Summary", stats)
        
        assert 'Test Summary' in box
        assert 'Cards' in box
        assert '100' in box
        assert 'Time' in box
        assert '30m' in box
    
    def test_achievement_display(self):
        """Test achievement display creation."""
        from srs.visualization import create_achievement_display
        
        achievements = [
            {
                'name': 'First Steps',
                'description': 'Complete first review',
                'icon': '👶',
                'unlocked_at': datetime.now()
            }
        ]
        
        display = create_achievement_display(achievements)
        
        assert 'First Steps' in display
        assert 'Complete first review' in display
        assert '👶' in display
    
    def test_export_data_to_csv(self):
        """Test CSV export functionality."""
        from srs.visualization import export_data_to_csv
        
        data = [
            {'name': 'Alice', 'score': 95},
            {'name': 'Bob', 'score': 87}
        ]
        
        output_file = Path(self.temp_dir) / 'test_export.csv'
        success = export_data_to_csv(data, str(output_file))
        
        assert success
        assert output_file.exists()
        
        # Verify CSV content
        with open(output_file, 'r') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            assert len(rows) == 2
            assert rows[0]['name'] == 'Alice'
            assert rows[0]['score'] == '95'
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """Clean up."""
        pass
