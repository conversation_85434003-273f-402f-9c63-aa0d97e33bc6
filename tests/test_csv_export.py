"""
Tests for the enhanced CSV export functionality.

This module tests the CSV export system including scheduling data,
metadata, and various export options.
"""

import pytest
import tempfile
import csv
import os
from datetime import datetime, timedelta
from unittest.mock import patch
from pathlib import Path

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.exporters.csv_exporter import CSVExporter
from srs.config import reset_config


class TestCSVExporter:
    """Test the CSV exporter functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db = Database(':memory:')
        reset_config()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_csv_exporter_initialization(self):
        """Test CSV exporter initialization."""
        exporter = CSVExporter()
        assert exporter is not None
    
    def test_get_supported_formats(self):
        """Test getting supported file formats."""
        exporter = CSVExporter()
        formats = exporter.get_supported_formats()
        assert '.csv' in formats
        assert '.tsv' in formats
    
    def test_export_deck_basic(self):
        """Test basic deck export."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and cards
            deck = Deck.create('Test Export')
            card1 = Card.create(deck.id, 'Question 1', 'Answer 1')
            card2 = Card.create(deck.id, 'Question 2', 'Answer 2')
            
            # Export deck
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'export.csv')
            
            result = exporter.export_deck('Test Export', output_path)
            
            assert result['deck_name'] == 'Test Export'
            assert result['exported_cards'] == 2
            assert os.path.exists(output_path)
            
            # Check CSV content
            with open(output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            assert len(rows) == 2
            assert rows[0]['front'] == 'Question 1'
            assert rows[0]['back'] == 'Answer 1'
            assert rows[1]['front'] == 'Question 2'
            assert rows[1]['back'] == 'Answer 2'
    
    def test_export_with_metadata(self):
        """Test export with metadata included."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and cards
            deck = Deck.create('Test Export')
            card = Card.create(deck.id, 'Question', 'Answer')
            card.difficulty = 0.8
            card.learning_state = 'graduated'
            card.save()
            
            # Export with metadata
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'export_meta.csv')
            
            result = exporter.export_deck('Test Export', output_path, include_metadata=True)
            
            assert result['exported_cards'] == 1
            
            # Check CSV content
            with open(output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            assert len(rows) == 1
            assert 'difficulty' in rows[0]
            assert 'learning_state' in rows[0]
            assert rows[0]['difficulty'] == '0.8'
            assert rows[0]['learning_state'] == 'graduated'
    
    def test_export_with_stats(self):
        """Test export with statistics included."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and cards
            deck = Deck.create('Test Export')
            card = Card.create(deck.id, 'Question', 'Answer')
            card.repetitions = 3
            card.interval = 7
            card.ease_factor = 2.3
            card.consecutive_correct = 2
            card.consecutive_incorrect = 1
            card.save()
            
            # Export with stats
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'export_stats.csv')
            
            result = exporter.export_deck('Test Export', output_path, include_stats=True)
            
            assert result['exported_cards'] == 1
            
            # Check CSV content
            with open(output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            assert len(rows) == 1
            assert 'repetitions' in rows[0]
            assert 'interval' in rows[0]
            assert 'ease_factor' in rows[0]
            assert 'consecutive_correct' in rows[0]
            assert 'consecutive_incorrect' in rows[0]
            assert rows[0]['repetitions'] == '3'
            assert rows[0]['interval'] == '7'
            assert rows[0]['ease_factor'] == '2.3'
    
    def test_export_with_custom_delimiter(self):
        """Test export with custom delimiter."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and cards
            deck = Deck.create('Test Export')
            Card.create(deck.id, 'Question', 'Answer')
            
            # Export with tab delimiter
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'export_tab.tsv')
            
            result = exporter.export_deck('Test Export', output_path, delimiter='\t')
            
            assert result['delimiter'] == '\t'
            
            # Check file content
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            assert '\t' in content
            assert ',' not in content.split('\n')[1]  # Data line shouldn't have commas
    
    def test_export_nonexistent_deck(self):
        """Test export of nonexistent deck."""
        with patch('srs.models.get_database', return_value=self.db):
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'export.csv')
            
            with pytest.raises(ValueError, match="not found"):
                exporter.export_deck('Nonexistent Deck', output_path)
    
    def test_export_multiple_decks(self):
        """Test exporting multiple decks."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test decks
            deck1 = Deck.create('Deck 1')
            deck2 = Deck.create('Deck 2')
            Card.create(deck1.id, 'Q1', 'A1')
            Card.create(deck2.id, 'Q2', 'A2')
            
            # Export multiple decks
            exporter = CSVExporter()
            output_dir = os.path.join(self.temp_dir, 'multi_export')
            
            result = exporter.export_multiple_decks(['Deck 1', 'Deck 2'], output_dir)
            
            assert len(result['exported_decks']) == 2
            assert result['total_cards'] == 2
            assert len(result['failed_decks']) == 0
            
            # Check files were created
            assert os.path.exists(os.path.join(output_dir, 'Deck 1.csv'))
            assert os.path.exists(os.path.join(output_dir, 'Deck 2.csv'))
    
    def test_export_all_decks_combined(self):
        """Test exporting all decks to a single file."""
        with patch('srs.models.get_database', return_value=self.db):
            with patch('srs.database.get_database', return_value=self.db):
                # Create test decks
                deck1 = Deck.create('Deck 1')
                deck2 = Deck.create('Deck 2')
                Card.create(deck1.id, 'Q1', 'A1')
                Card.create(deck2.id, 'Q2', 'A2')
                
                # Export all decks combined
                exporter = CSVExporter()
                output_path = os.path.join(self.temp_dir, 'all_decks.csv')
                
                result = exporter.export_all_decks_combined(output_path, include_deck_name=True)
                
                assert result['exported_decks'] == 2
                assert result['exported_cards'] == 2
                
                # Check CSV content
                with open(output_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                
                assert len(rows) == 2
                assert 'deck_name' in rows[0]
                assert rows[0]['deck_name'] == 'Deck 1'
                assert rows[1]['deck_name'] == 'Deck 2'
    
    def test_preview_export(self):
        """Test export preview functionality."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and cards
            deck = Deck.create('Test Preview')
            for i in range(5):
                card = Card.create(deck.id, f'Question {i+1}', f'Answer {i+1}')
                card.difficulty = 0.5 + (i * 0.1)
                card.repetitions = i
                card.interval = i + 1
                card.save()
            
            # Generate preview
            exporter = CSVExporter()
            preview = exporter.preview_export('Test Preview', max_cards=3)
            
            assert len(preview) == 5  # Header + 3 cards + "... more" row
            assert preview[0] == ['front', 'back', 'difficulty', 'repetitions', 'interval']
            assert preview[1][0] == 'Question 1'
            assert preview[3][0] == 'Question 3'
            assert 'and 2 more cards' in preview[4][1]
    
    def test_full_export_with_all_data(self):
        """Test full export with all available data."""
        with patch('srs.models.get_database', return_value=self.db):
            # Create test deck and card with all data
            deck = Deck.create('Full Export Test')
            card = Card.create(deck.id, 'Complex Question', 'Complex Answer')
            
            # Set all possible card attributes
            card.difficulty = 0.75
            card.learning_state = 'graduated'
            card.repetitions = 5
            card.interval = 14
            card.ease_factor = 2.8
            card.consecutive_correct = 3
            card.consecutive_incorrect = 1
            card.due_date = datetime.now() + timedelta(days=7)
            card.save()
            
            # Export with all data
            exporter = CSVExporter()
            output_path = os.path.join(self.temp_dir, 'full_export.csv')
            
            result = exporter.export_deck(
                'Full Export Test', 
                output_path, 
                include_metadata=True, 
                include_stats=True
            )
            
            assert result['exported_cards'] == 1
            
            # Check CSV content has all columns
            with open(output_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                headers = reader.fieldnames
            
            expected_columns = [
                'front', 'back', 'difficulty', 'learning_state',
                'repetitions', 'interval', 'ease_factor',
                'consecutive_correct', 'consecutive_incorrect',
                'due_date', 'created_at'
            ]
            
            for col in expected_columns:
                assert col in headers
            
            # Check data values
            row = rows[0]
            assert row['front'] == 'Complex Question'
            assert row['back'] == 'Complex Answer'
            assert row['difficulty'] == '0.75'
            assert row['learning_state'] == 'graduated'
            assert row['repetitions'] == '5'
            assert row['interval'] == '14'
            assert row['ease_factor'] == '2.8'
            assert row['consecutive_correct'] == '3'
            assert row['consecutive_incorrect'] == '1'
            assert row['due_date'] != ''  # Should have a date
            assert row['created_at'] != ''  # Should have a date
