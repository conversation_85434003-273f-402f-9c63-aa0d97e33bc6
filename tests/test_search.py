"""
Tests for the search functionality.

This module tests the search engine including query parsing,
filtering, ranking, and result formatting.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timedelta

from srs.database import Database, reset_database
from srs.models import Deck, Card
from srs.search import SearchEngine, SearchQuery, SearchField, SearchFilter, SearchOperator
from srs.config import reset_config


class TestSearchEngine:
    """Test the search engine functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.db = Database(':memory:')
        reset_config()
        
        # Create test data
        with patch('srs.models.get_database', return_value=self.db):
            with patch('srs.search.get_database', return_value=self.db):
                # Create test decks
                self.deck1 = Deck.create('Spanish Vocabulary')
                self.deck2 = Deck.create('French Vocabulary')
                
                # Create test cards
                self.card1 = Card.create(self.deck1.id, 'hola', 'hello')
                self.card1.difficulty = 0.3
                self.card1.repetitions = 2
                self.card1.interval = 3
                self.card1.learning_state = 'graduated'
                self.card1.save()
                
                self.card2 = Card.create(self.deck1.id, 'comer', 'to eat')
                self.card2.difficulty = 0.7
                self.card2.repetitions = 0
                self.card2.interval = 1
                self.card2.learning_state = 'new'
                self.card2.save()
                
                self.card3 = Card.create(self.deck2.id, 'bonjour', 'hello')
                self.card3.difficulty = 0.5
                self.card3.repetitions = 1
                self.card3.interval = 2
                self.card3.learning_state = 'learning'
                self.card3.save()
                
                self.card4 = Card.create(self.deck1.id, 'hablar', 'to speak')
                self.card4.difficulty = 0.4
                self.card4.repetitions = 3
                self.card4.interval = 7
                self.card4.learning_state = 'graduated'
                self.card4.save()
    
    def teardown_method(self):
        """Clean up."""
        reset_config()
    
    def test_search_engine_initialization(self):
        """Test search engine initialization."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            assert engine.db == self.db
    
    def test_basic_text_search(self):
        """Test basic text search functionality."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hello')
            
            assert len(results) == 2  # hola->hello and bonjour->hello
            
            # Check that results contain the expected cards
            card_fronts = [result.card.front for result in results]
            assert 'hola' in card_fronts
            assert 'bonjour' in card_fronts
    
    def test_deck_filtered_search(self):
        """Test search with deck filtering."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hello', deck_name='Spanish Vocabulary')
            
            assert len(results) == 1
            assert results[0].card.front == 'hola'
            assert results[0].deck_name == 'Spanish Vocabulary'
    
    def test_difficulty_filter_search(self):
        """Test search with difficulty filtering."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('difficulty:>0.5')
            
            # Should find comer (0.7)
            assert len(results) == 1
            assert results[0].card.front == 'comer'
            assert results[0].card.difficulty == 0.7
    
    def test_interval_filter_search(self):
        """Test search with interval filtering."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('interval:>=7')
            
            # Should find hablar (interval 7)
            assert len(results) == 1
            assert results[0].card.front == 'hablar'
            assert results[0].card.interval == 7
    
    def test_learning_state_filter_search(self):
        """Test search with learning state filtering."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('state:graduated')
            
            # Should find hola and hablar
            assert len(results) == 2
            card_fronts = [result.card.front for result in results]
            assert 'hola' in card_fronts
            assert 'hablar' in card_fronts
    
    def test_front_field_search(self):
        """Test search in front field only."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('front:"hola"')
            
            assert len(results) == 1
            assert results[0].card.front == 'hola'
    
    def test_back_field_search(self):
        """Test search in back field only."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('back:"to eat"')
            
            assert len(results) == 1
            assert results[0].card.front == 'comer'
            assert results[0].card.back == 'to eat'
    
    def test_combined_filters_search(self):
        """Test search with multiple filters."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('difficulty:<0.5 state:graduated')

            # Should find hola (difficulty 0.3, graduated) and hablar (difficulty 0.4, graduated)
            assert len(results) == 2
            card_fronts = [result.card.front for result in results]
            assert 'hola' in card_fronts
            assert 'hablar' in card_fronts
    
    def test_or_operator_search(self):
        """Test search with OR operator."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hola OR bonjour')
            
            assert len(results) == 2
            card_fronts = [result.card.front for result in results]
            assert 'hola' in card_fronts
            assert 'bonjour' in card_fronts
    
    def test_and_operator_search(self):
        """Test search with AND operator."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('to AND speak')
            
            # Should find hablar (to speak)
            assert len(results) == 1
            assert results[0].card.front == 'hablar'
            assert results[0].card.back == 'to speak'
    
    def test_quoted_phrase_search(self):
        """Test search with quoted phrases."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('"to speak"')
            
            assert len(results) == 1
            assert results[0].card.front == 'hablar'
            assert results[0].card.back == 'to speak'
    
    def test_search_result_ranking(self):
        """Test search result ranking."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hello')
            
            # Results should be ranked by relevance
            assert len(results) == 2
            
            # Check that scores are assigned
            for result in results:
                assert result.score > 0
            
            # Results should be sorted by score (descending)
            if len(results) > 1:
                assert results[0].score >= results[1].score
    
    def test_search_with_matches(self):
        """Test search result match highlighting."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hello')
            
            # Check that matches are recorded
            found_match = False
            for result in results:
                if result.matches:
                    found_match = True
                    # Should have matches in back field
                    assert 'back' in result.matches
                    # Matches should contain the search term
                    for match in result.matches['back']:
                        assert 'hello' in match.lower()
            
            assert found_match
    
    def test_search_pagination(self):
        """Test search pagination."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            
            # Search with limit
            results = engine.search('', limit=2)
            assert len(results) <= 2
            
            # Search with offset
            all_results = engine.search('', limit=10)
            offset_results = engine.search('', limit=2, offset=1)
            
            if len(all_results) > 1:
                assert len(offset_results) <= 2
    
    def test_empty_search_query(self):
        """Test search with empty query."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('')
            
            # Should return all cards
            assert len(results) == 4
    
    def test_no_results_search(self):
        """Test search that returns no results."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('nonexistent')
            
            assert len(results) == 0
    
    def test_search_suggestions(self):
        """Test search suggestions functionality."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            
            # Test field suggestions
            suggestions = engine.get_search_suggestions('tag')
            assert 'tag:' in suggestions
            
            # Test partial content suggestions
            suggestions = engine.get_search_suggestions('ho')
            # Should include suggestions based on card content
            assert len(suggestions) >= 0  # May or may not have suggestions
    
    def test_query_parsing(self):
        """Test search query parsing."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            
            # Test basic query parsing
            query = engine._parse_query('hello world')
            assert 'hello' in query.text_terms
            assert 'world' in query.text_terms
            
            # Test filter parsing
            query = engine._parse_query('difficulty:>0.5')
            assert len(query.filters) == 1
            assert query.filters[0].field == SearchField.DIFFICULTY
            assert query.filters[0].operator == '>'
            assert query.filters[0].value == 0.5
            
            # Test quoted phrase parsing
            query = engine._parse_query('"hello world"')
            assert 'hello world' in query.text_terms
    
    def test_complex_search_query(self):
        """Test complex search query with multiple elements."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            results = engine.search('hello difficulty:>0.2 state:graduated')
            
            # Should find hola (hello, difficulty 0.3, graduated)
            assert len(results) == 1
            assert results[0].card.front == 'hola'
    
    def test_case_insensitive_search(self):
        """Test that search is case insensitive."""
        with patch('srs.search.get_database', return_value=self.db):
            engine = SearchEngine()
            
            # Test different cases
            results_lower = engine.search('hello')
            results_upper = engine.search('HELLO')
            results_mixed = engine.search('Hello')
            
            # Should return same results regardless of case
            assert len(results_lower) == len(results_upper) == len(results_mixed)
            
            if results_lower:
                assert results_lower[0].card.id == results_upper[0].card.id
                assert results_lower[0].card.id == results_mixed[0].card.id
