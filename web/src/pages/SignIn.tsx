import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { AuthForm, type AuthFormData } from '@/components/auth/AuthForm'
import { useAuthActions } from '@/store/auth'

export function SignIn() {
  const navigate = useNavigate()
  const { signIn } = useAuthActions()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (data: AuthFormData) => {
    setError('')
    setLoading(true)

    try {
      await signIn(data.email, data.password)
      navigate('/')
    } catch (err: any) {
      setError(err.message || 'Failed to sign in')
      throw err // Re-throw to let AuthForm handle loading state
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthForm
      mode="signin"
      onSubmit={handleSubmit}
      loading={loading}
      error={error}
    />
  )
}
