import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { AuthForm, type AuthFormData } from '@/components/auth/AuthForm'
import { useAuthActions } from '@/store/auth'

export function SignUp() {
  const navigate = useNavigate()
  const { signUp } = useAuthActions()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (data: AuthFormData) => {
    setError('')
    setLoading(true)

    try {
      await signUp(
        data.email,
        data.password,
        data.username || undefined,
        data.fullName || undefined
      )
      navigate('/')
    } catch (err: any) {
      setError(err.message || 'Failed to create account')
      throw err // Re-throw to let AuthForm handle loading state
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthForm
      mode="signup"
      onSubmit={handleSubmit}
      loading={loading}
      error={error}
    />
  )
}
