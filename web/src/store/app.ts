import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Deck {
  id: string
  name: string
  description: string
  cardCount: number
  dueCount: number
  newCount: number
  learningCount: number
}

interface Card {
  id: string
  deckId: string
  front: string
  back: string
  interval: number
  repetitions: number
  easeFactor: number
  dueDate: string
  difficulty: number
  learningState: 'new' | 'learning' | 'review' | 'relearning'
  currentStep: number
}

interface ReviewSession {
  id: string
  deckId: string
  cards: Card[]
  currentIndex: number
  startTime: Date
  endTime?: Date
  totalReviews: number
  correctReviews: number
}

interface AppState {
  // UI State
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  
  // Data State
  decks: Deck[]
  currentDeck: Deck | null
  cards: Card[]
  currentReviewSession: ReviewSession | null
  
  // Loading States
  loading: {
    decks: boolean
    cards: boolean
    review: boolean
  }
  
  // Error States
  error: string | null
}

interface AppActions {
  // UI Actions
  setSidebarOpen: (open: boolean) => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  
  // Data Actions
  setDecks: (decks: Deck[]) => void
  setCurrentDeck: (deck: Deck | null) => void
  setCards: (cards: Card[]) => void
  addDeck: (deck: Deck) => void
  updateDeck: (id: string, updates: Partial<Deck>) => void
  removeDeck: (id: string) => void
  addCard: (card: Card) => void
  updateCard: (id: string, updates: Partial<Card>) => void
  removeCard: (id: string) => void
  
  // Review Session Actions
  startReviewSession: (deckId: string, cards: Card[]) => void
  endReviewSession: () => void
  nextCard: () => void
  previousCard: () => void
  submitReview: (rating: number) => void
  
  // Loading Actions
  setLoading: (key: keyof AppState['loading'], loading: boolean) => void
  
  // Error Actions
  setError: (error: string | null) => void
  clearError: () => void
  
  // Reset
  reset: () => void
}

type AppStore = AppState & AppActions

const initialState: AppState = {
  sidebarOpen: true,
  theme: 'system',
  decks: [],
  currentDeck: null,
  cards: [],
  currentReviewSession: null,
  loading: {
    decks: false,
    cards: false,
    review: false,
  },
  error: null,
}

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // UI Actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      setTheme: (theme) => set({ theme }),

      // Data Actions
      setDecks: (decks) => set({ decks }),
      setCurrentDeck: (deck) => set({ currentDeck: deck }),
      setCards: (cards) => set({ cards }),

      addDeck: (deck) => {
        const { decks } = get()
        set({ decks: [...decks, deck] })
      },

      updateDeck: (id, updates) => {
        const { decks, currentDeck } = get()
        const updatedDecks = decks.map((deck) =>
          deck.id === id ? { ...deck, ...updates } : deck
        )
        set({
          decks: updatedDecks,
          currentDeck: currentDeck?.id === id ? { ...currentDeck, ...updates } : currentDeck,
        })
      },

      removeDeck: (id) => {
        const { decks, currentDeck } = get()
        set({
          decks: decks.filter((deck) => deck.id !== id),
          currentDeck: currentDeck?.id === id ? null : currentDeck,
        })
      },

      addCard: (card) => {
        const { cards } = get()
        set({ cards: [...cards, card] })
      },

      updateCard: (id, updates) => {
        const { cards } = get()
        const updatedCards = cards.map((card) =>
          card.id === id ? { ...card, ...updates } : card
        )
        set({ cards: updatedCards })
      },

      removeCard: (id) => {
        const { cards } = get()
        set({ cards: cards.filter((card) => card.id !== id) })
      },

      // Review Session Actions
      startReviewSession: (deckId, cards) => {
        const session: ReviewSession = {
          id: crypto.randomUUID(),
          deckId,
          cards,
          currentIndex: 0,
          startTime: new Date(),
          totalReviews: 0,
          correctReviews: 0,
        }
        set({ currentReviewSession: session })
      },

      endReviewSession: () => {
        const { currentReviewSession } = get()
        if (currentReviewSession) {
          set({
            currentReviewSession: {
              ...currentReviewSession,
              endTime: new Date(),
            },
          })
          // Clear session after a delay to show results
          setTimeout(() => {
            set({ currentReviewSession: null })
          }, 3000)
        }
      },

      nextCard: () => {
        const { currentReviewSession } = get()
        if (currentReviewSession && currentReviewSession.currentIndex < currentReviewSession.cards.length - 1) {
          set({
            currentReviewSession: {
              ...currentReviewSession,
              currentIndex: currentReviewSession.currentIndex + 1,
            },
          })
        }
      },

      previousCard: () => {
        const { currentReviewSession } = get()
        if (currentReviewSession && currentReviewSession.currentIndex > 0) {
          set({
            currentReviewSession: {
              ...currentReviewSession,
              currentIndex: currentReviewSession.currentIndex - 1,
            },
          })
        }
      },

      submitReview: (rating) => {
        const { currentReviewSession } = get()
        if (currentReviewSession) {
          const isCorrect = rating >= 3 // Assuming 3+ is correct
          set({
            currentReviewSession: {
              ...currentReviewSession,
              totalReviews: currentReviewSession.totalReviews + 1,
              correctReviews: currentReviewSession.correctReviews + (isCorrect ? 1 : 0),
            },
          })
        }
      },

      // Loading Actions
      setLoading: (key, loading) => {
        const { loading: currentLoading } = get()
        set({
          loading: {
            ...currentLoading,
            [key]: loading,
          },
        })
      },

      // Error Actions
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // Reset
      reset: () => set(initialState),
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        sidebarOpen: state.sidebarOpen,
        theme: state.theme,
      }),
    }
  )
)

// Selectors for common use cases
export const useDecks = () => {
  const { decks, loading, error } = useAppStore()
  return { decks, loading: loading.decks, error }
}

export const useCurrentDeck = () => {
  const { currentDeck } = useAppStore()
  return currentDeck
}

export const useCards = () => {
  const { cards, loading, error } = useAppStore()
  return { cards, loading: loading.cards, error }
}

export const useReviewSession = () => {
  const { currentReviewSession } = useAppStore()
  return currentReviewSession
}

export const useUI = () => {
  const { sidebarOpen, theme, setSidebarOpen, setTheme } = useAppStore()
  return { sidebarOpen, theme, setSidebarOpen, setTheme }
}
