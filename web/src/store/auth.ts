import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, Session } from '@supabase/supabase-js'
import { AuthService } from '@/lib/auth'

interface AuthState {
  user: User | null
  session: Session | null
  loading: boolean
  initialized: boolean
}

interface AuthActions {
  setUser: (user: User | null) => void
  setSession: (session: Session | null) => void
  setLoading: (loading: boolean) => void
  setInitialized: (initialized: boolean) => void
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, username?: string, fullName?: string) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  updatePassword: (password: string) => Promise<void>
  initialize: () => Promise<void>
  reset: () => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      session: null,
      loading: false,
      initialized: false,

      // Actions
      setUser: (user) => set({ user }),
      setSession: (session) => set({ session }),
      setLoading: (loading) => set({ loading }),
      setInitialized: (initialized) => set({ initialized }),

      signIn: async (email, password) => {
        set({ loading: true })
        try {
          const { user, session } = await AuthService.signIn({ email, password })
          set({ user, session, loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      signUp: async (email, password, username, fullName) => {
        set({ loading: true })
        try {
          const { user, session } = await AuthService.signUp({
            email,
            password,
            username,
            fullName,
          })
          set({ user, session, loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      signOut: async () => {
        set({ loading: true })
        try {
          await AuthService.signOut()
          set({ user: null, session: null, loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      resetPassword: async (email) => {
        set({ loading: true })
        try {
          await AuthService.resetPassword(email)
          set({ loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      updatePassword: async (password) => {
        set({ loading: true })
        try {
          await AuthService.updatePassword(password)
          set({ loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      initialize: async () => {
        if (get().initialized) return

        set({ loading: true })
        try {
          const session = await AuthService.getSession()
          const user = session?.user || null

          set({
            user,
            session,
            loading: false,
            initialized: true,
          })

          // Set up auth state listener
          AuthService.onAuthStateChange((event, session) => {
            set({
              user: session?.user || null,
              session,
            })
          })
        } catch (error) {
          console.error('Auth initialization error:', error)
          set({
            user: null,
            session: null,
            loading: false,
            initialized: true,
          })
        }
      },

      reset: () => {
        set({
          user: null,
          session: null,
          loading: false,
          initialized: false,
        })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        initialized: state.initialized,
      }),
    }
  )
)

// Selectors for common use cases
export const useAuth = () => {
  const { user, session, loading, initialized } = useAuthStore()
  return {
    user,
    session,
    loading,
    initialized,
    isAuthenticated: !!user && !!session,
  }
}

export const useAuthActions = () => {
  const {
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    initialize,
    reset,
  } = useAuthStore()

  return {
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    initialize,
    reset,
  }
}
