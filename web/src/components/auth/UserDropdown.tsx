import React, { useState, useRef, useEffect } from 'react'
import { User, Settings, LogOut, ChevronDown, Shield } from 'lucide-react'
import { useAuth, useAuthActions } from '@/store/auth'
import { ProfileModal } from './ProfileModal'
import { cn, getInitials } from '@/lib/utils'

export function UserDropdown() {
  const { user } = useAuth()
  const { signOut } = useAuthActions()
  const [isOpen, setIsOpen] = useState(false)
  const [showProfileModal, setShowProfileModal] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const menuItems = [
    {
      icon: User,
      label: 'Profile',
      onClick: () => {
        setShowProfileModal(true)
        setIsOpen(false)
      },
    },
    {
      icon: Settings,
      label: 'Settings',
      onClick: () => {
        // Navigate to settings page
        setIsOpen(false)
      },
    },
    {
      icon: Shield,
      label: 'Privacy',
      onClick: () => {
        // Navigate to privacy page
        setIsOpen(false)
      },
    },
    {
      icon: LogOut,
      label: 'Sign out',
      onClick: handleSignOut,
      className: 'text-error-600 hover:text-error-700 hover:bg-error-50',
    },
  ]

  if (!user) return null

  const displayName = user.user_metadata?.full_name || user.user_metadata?.username || user.email
  const avatarUrl = user.user_metadata?.avatar_url

  return (
    <>
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center space-x-2">
            {/* Avatar */}
            <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center overflow-hidden">
              {avatarUrl ? (
                <img
                  src={avatarUrl}
                  alt="Avatar"
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-xs font-medium text-white">
                  {getInitials(displayName || 'U')}
                </span>
              )}
            </div>
            
            {/* User Info */}
            <div className="hidden sm:block text-left">
              <p className="text-sm font-medium text-gray-700 truncate max-w-32">
                {displayName}
              </p>
              <p className="text-xs text-gray-500 truncate max-w-32">
                {user.email}
              </p>
            </div>
          </div>
          
          <ChevronDown
            className={cn(
              'h-4 w-4 text-gray-400 transition-transform',
              isOpen && 'transform rotate-180'
            )}
          />
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
            {/* User Info Header */}
            <div className="px-4 py-3 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-900 truncate">
                {displayName}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user.email}
              </p>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              {menuItems.map((item, index) => (
                <button
                  key={index}
                  onClick={item.onClick}
                  className={cn(
                    'w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors',
                    item.className
                  )}
                >
                  <item.icon className="h-4 w-4 mr-3" />
                  {item.label}
                </button>
              ))}
            </div>

            {/* Footer */}
            <div className="border-t border-gray-100 px-4 py-2">
              <p className="text-xs text-gray-500">
                Signed in as {user.email}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Profile Modal */}
      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
      />
    </>
  )
}
