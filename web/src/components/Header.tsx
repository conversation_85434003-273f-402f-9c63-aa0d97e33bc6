import React from 'react'
import { <PERSON><PERSON>, <PERSON> } from 'lucide-react'
import { useUI } from '@/store/app'
import { useAuth } from '@/store/auth'
import { UserDropdown } from '@/components/auth/UserDropdown'

export function Header() {
  const { sidebarOpen, setSidebarOpen } = useUI()
  const { isAuthenticated } = useAuth()

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900">
            Spaced Repetition System
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          {isAuthenticated && (
            <>
              <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </button>

              <UserDropdown />
            </>
          )}
        </div>
      </div>
    </header>
  )
}
