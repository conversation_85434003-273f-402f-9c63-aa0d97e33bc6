import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types (will be generated from Supabase CLI)
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string | null
          email: string | null
          full_name: string | null
          avatar_url: string | null
          preferences: Record<string, any> | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username?: string | null
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          preferences?: Record<string, any> | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string | null
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          preferences?: Record<string, any> | null
          created_at?: string
          updated_at?: string
        }
      }
      decks: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string
          daily_new_limit: number
          daily_review_limit: number
          settings: Record<string, any> | null
          learning_steps: string
          graduation_interval: number
          easy_interval: number
          relearning_steps: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string
          daily_new_limit?: number
          daily_review_limit?: number
          settings?: Record<string, any> | null
          learning_steps?: string
          graduation_interval?: number
          easy_interval?: number
          relearning_steps?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string
          daily_new_limit?: number
          daily_review_limit?: number
          settings?: Record<string, any> | null
          learning_steps?: string
          graduation_interval?: number
          easy_interval?: number
          relearning_steps?: string
          created_at?: string
          updated_at?: string
        }
      }
      cards: {
        Row: {
          id: string
          deck_id: string
          front: string
          back: string
          interval: number
          repetitions: number
          ease_factor: number
          due_date: string
          difficulty: number
          last_review_duration: number
          consecutive_correct: number
          consecutive_incorrect: number
          learning_state: string
          current_step: number
          step_due_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          deck_id: string
          front: string
          back: string
          interval?: number
          repetitions?: number
          ease_factor?: number
          due_date?: string
          difficulty?: number
          last_review_duration?: number
          consecutive_correct?: number
          consecutive_incorrect?: number
          learning_state?: string
          current_step?: number
          step_due_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          deck_id?: string
          front?: string
          back?: string
          interval?: number
          repetitions?: number
          ease_factor?: number
          due_date?: string
          difficulty?: number
          last_review_duration?: number
          consecutive_correct?: number
          consecutive_incorrect?: number
          learning_state?: string
          current_step?: number
          step_due_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      reviews: {
        Row: {
          id: string
          card_id: string
          rating: number
          review_duration: number
          session_id: string | null
          review_type: string
          reviewed_at: string
        }
        Insert: {
          id?: string
          card_id: string
          rating: number
          review_duration?: number
          session_id?: string | null
          review_type?: string
          reviewed_at?: string
        }
        Update: {
          id?: string
          card_id?: string
          rating?: number
          review_duration?: number
          session_id?: string | null
          review_type?: string
          reviewed_at?: string
        }
      }
    }
  }
}
