"""
Search engine for the spaced repetition system.

This module provides comprehensive search functionality including
full-text search, tag filtering, metadata search, and result ranking.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .models import Card, Deck
from .database import get_database

logger = logging.getLogger(__name__)


class SearchOperator(Enum):
    """Search operators for query parsing."""
    AND = "AND"
    OR = "OR"
    NOT = "NOT"


class SearchField(Enum):
    """Searchable fields."""
    CONTENT = "content"
    FRONT = "front"
    BACK = "back"
    TAG = "tag"
    DIFFICULTY = "difficulty"
    INTERVAL = "interval"
    REPETITIONS = "repetitions"
    EASE_FACTOR = "ease_factor"
    LEARNING_STATE = "learning_state"
    DECK = "deck"


@dataclass
class SearchFilter:
    """Search filter specification."""
    field: SearchField
    operator: str  # =, >, <, >=, <=, !=
    value: Any
    negate: bool = False


@dataclass
class SearchQuery:
    """Parsed search query."""
    text_terms: List[str]
    filters: List[SearchFilter]
    operators: List[SearchOperator]
    deck_filter: Optional[str] = None


@dataclass
class SearchResult:
    """Search result with ranking."""
    card: Card
    score: float
    matches: Dict[str, List[str]]  # Field -> list of matching snippets
    deck_name: str


class SearchEngine:
    """
    Comprehensive search engine for cards.
    
    Provides full-text search, filtering, and result ranking.
    """
    
    def __init__(self):
        """Initialize the search engine."""
        self.db = get_database()
    
    def search(self, query_string: str, deck_name: Optional[str] = None,
              limit: int = 50, offset: int = 0) -> List[SearchResult]:
        """
        Search for cards using a query string.
        
        Args:
            query_string: Search query string
            deck_name: Optional deck name to filter by
            limit: Maximum number of results
            offset: Result offset for pagination
            
        Returns:
            List of search results ordered by relevance
        """
        logger.debug(f"Searching: '{query_string}' in deck: {deck_name}")
        
        # Parse the query
        query = self._parse_query(query_string)
        if deck_name:
            query.deck_filter = deck_name
        
        # Execute search
        results = self._execute_search(query, limit, offset)
        
        # Rank results
        ranked_results = self._rank_results(results, query)
        
        logger.info(f"Search returned {len(ranked_results)} results")
        return ranked_results
    
    def _parse_query(self, query_string: str) -> SearchQuery:
        """Parse search query string into structured query."""
        query = SearchQuery(text_terms=[], filters=[], operators=[])
        
        # Split by operators while preserving them
        parts = re.split(r'\s+(AND|OR|NOT)\s+', query_string, flags=re.IGNORECASE)
        
        current_operator = None
        for i, part in enumerate(parts):
            part = part.strip()
            if not part:
                continue
            
            # Check if this part is an operator
            if part.upper() in ['AND', 'OR', 'NOT']:
                current_operator = SearchOperator(part.upper())
                if current_operator != SearchOperator.NOT:
                    query.operators.append(current_operator)
                continue
            
            # Parse filters and text terms
            filters, text_terms = self._parse_query_part(part, current_operator == SearchOperator.NOT)
            query.filters.extend(filters)
            query.text_terms.extend(text_terms)
            
            current_operator = None
        
        return query
    
    def _parse_query_part(self, part: str, negate: bool = False) -> Tuple[List[SearchFilter], List[str]]:
        """Parse a single part of the query."""
        filters = []
        text_terms = []
        
        # Look for field:value patterns
        field_patterns = [
            (r'tag:(\w+)', SearchField.TAG, '='),
            (r'difficulty:([<>=!]+)?([\d.]+)', SearchField.DIFFICULTY, None),
            (r'interval:([<>=!]+)?(\d+)', SearchField.INTERVAL, None),
            (r'repetitions:([<>=!]+)?(\d+)', SearchField.REPETITIONS, None),
            (r'ease:([<>=!]+)?([\d.]+)', SearchField.EASE_FACTOR, None),
            (r'state:(\w+)', SearchField.LEARNING_STATE, '='),
            (r'deck:(\w+)', SearchField.DECK, '='),
            (r'front:"([^"]+)"', SearchField.FRONT, '='),
            (r'back:"([^"]+)"', SearchField.BACK, '='),
        ]
        
        remaining_text = part
        
        for pattern, field, default_op in field_patterns:
            matches = re.finditer(pattern, part, re.IGNORECASE)
            for match in matches:
                if field in [SearchField.DIFFICULTY, SearchField.INTERVAL, 
                           SearchField.REPETITIONS, SearchField.EASE_FACTOR]:
                    # Numeric fields with operators
                    operator = match.group(1) or '='
                    value = float(match.group(2)) if field in [SearchField.DIFFICULTY, SearchField.EASE_FACTOR] else int(match.group(2))
                else:
                    # String fields
                    operator = default_op
                    value = match.group(1)
                
                filters.append(SearchFilter(field, operator, value, negate))
                
                # Remove this match from remaining text
                remaining_text = remaining_text.replace(match.group(0), ' ')
        
        # Extract quoted strings and regular words from remaining text
        quoted_strings = re.findall(r'"([^"]+)"', remaining_text)
        remaining_text = re.sub(r'"[^"]+"', ' ', remaining_text)
        
        # Split remaining text into words
        words = [word.strip() for word in remaining_text.split() if word.strip()]
        
        text_terms.extend(quoted_strings)
        text_terms.extend(words)
        
        return filters, text_terms
    
    def _execute_search(self, query: SearchQuery, limit: int, offset: int) -> List[Tuple[Card, Dict[str, Any]]]:
        """Execute the search query against the database."""
        # Build SQL query
        sql_parts = []
        params = []
        
        # Base query
        base_sql = """
        SELECT c.*, d.name as deck_name
        FROM cards c
        JOIN decks d ON c.deck_id = d.id
        WHERE 1=1
        """
        sql_parts.append(base_sql)
        
        # Add deck filter
        if query.deck_filter:
            sql_parts.append("AND d.name = ?")
            params.append(query.deck_filter)
        
        # Add text search
        if query.text_terms:
            text_conditions = []
            for term in query.text_terms:
                text_conditions.append("(c.front LIKE ? OR c.back LIKE ?)")
                params.extend([f"%{term}%", f"%{term}%"])
            
            if query.operators and SearchOperator.OR in query.operators:
                sql_parts.append(f"AND ({' OR '.join(text_conditions)})")
            else:
                sql_parts.append(f"AND ({' AND '.join(text_conditions)})")
        
        # Add filters
        for filter_obj in query.filters:
            if filter_obj.field == SearchField.DIFFICULTY:
                sql_parts.append(f"AND c.difficulty {filter_obj.operator} ?")
                params.append(filter_obj.value)
            elif filter_obj.field == SearchField.INTERVAL:
                sql_parts.append(f"AND c.interval {filter_obj.operator} ?")
                params.append(filter_obj.value)
            elif filter_obj.field == SearchField.REPETITIONS:
                sql_parts.append(f"AND c.repetitions {filter_obj.operator} ?")
                params.append(filter_obj.value)
            elif filter_obj.field == SearchField.EASE_FACTOR:
                sql_parts.append(f"AND c.ease_factor {filter_obj.operator} ?")
                params.append(filter_obj.value)
            elif filter_obj.field == SearchField.LEARNING_STATE:
                sql_parts.append(f"AND c.learning_state = ?")
                params.append(filter_obj.value)
            elif filter_obj.field == SearchField.FRONT:
                sql_parts.append(f"AND c.front LIKE ?")
                params.append(f"%{filter_obj.value}%")
            elif filter_obj.field == SearchField.BACK:
                sql_parts.append(f"AND c.back LIKE ?")
                params.append(f"%{filter_obj.value}%")
            elif filter_obj.field == SearchField.DECK:
                sql_parts.append(f"AND d.name LIKE ?")
                params.append(f"%{filter_obj.value}%")
        
        # Add ordering and limits
        sql_parts.append("ORDER BY c.created_at DESC")
        sql_parts.append(f"LIMIT {limit} OFFSET {offset}")
        
        # Execute query
        full_sql = " ".join(sql_parts)
        results = self.db.execute_query(full_sql, params)
        
        # Convert to Card objects
        cards_with_meta = []
        for row in results:
            card = Card(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                due_date=row['due_date'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                created_at=row['created_at'],
                difficulty=row['difficulty'] if 'difficulty' in row.keys() else 0.5,
                last_review_duration=row['last_review_duration'] if 'last_review_duration' in row.keys() else 0,
                consecutive_correct=row['consecutive_correct'] if 'consecutive_correct' in row.keys() else 0,
                consecutive_incorrect=row['consecutive_incorrect'] if 'consecutive_incorrect' in row.keys() else 0,
                learning_state=row['learning_state'] if 'learning_state' in row.keys() else 'new',
                current_step=row['current_step'] if 'current_step' in row.keys() else 0,
                step_due_date=row['step_due_date'] if 'step_due_date' in row.keys() and row['step_due_date'] else None
            )
            
            meta = {'deck_name': row['deck_name']}
            cards_with_meta.append((card, meta))
        
        return cards_with_meta
    
    def _rank_results(self, results: List[Tuple[Card, Dict[str, Any]]], 
                     query: SearchQuery) -> List[SearchResult]:
        """Rank search results by relevance."""
        search_results = []
        
        for card, meta in results:
            score = 0.0
            matches = {}
            
            # Score text matches
            for term in query.text_terms:
                term_lower = term.lower()
                
                # Front matches
                front_matches = self._find_matches(card.front.lower(), term_lower)
                if front_matches:
                    matches['front'] = front_matches
                    score += len(front_matches) * 2.0  # Front matches weighted higher
                
                # Back matches
                back_matches = self._find_matches(card.back.lower(), term_lower)
                if back_matches:
                    matches['back'] = back_matches
                    score += len(back_matches) * 1.0
            
            # Boost score for exact matches
            for term in query.text_terms:
                if term.lower() in card.front.lower():
                    score += 5.0
                if term.lower() in card.back.lower():
                    score += 3.0
            
            # Boost score for newer cards
            score += 0.1
            
            # Boost score for cards with higher difficulty (more important)
            score += card.difficulty * 0.5
            
            search_result = SearchResult(
                card=card,
                score=score,
                matches=matches,
                deck_name=meta['deck_name']
            )
            search_results.append(search_result)
        
        # Sort by score (descending)
        search_results.sort(key=lambda x: x.score, reverse=True)
        
        return search_results
    
    def _find_matches(self, text: str, term: str) -> List[str]:
        """Find matching snippets in text."""
        matches = []
        start = 0
        
        while True:
            pos = text.find(term, start)
            if pos == -1:
                break
            
            # Extract snippet around match
            snippet_start = max(0, pos - 20)
            snippet_end = min(len(text), pos + len(term) + 20)
            snippet = text[snippet_start:snippet_end]
            
            # Highlight the match
            highlighted = snippet.replace(term, f"**{term}**")
            matches.append(highlighted)
            
            start = pos + 1
        
        return matches
    
    def get_search_suggestions(self, partial_query: str, limit: int = 10) -> List[str]:
        """Get search suggestions based on partial query."""
        suggestions = []
        
        # Add field suggestions
        field_suggestions = [
            "tag:", "difficulty:", "interval:", "repetitions:",
            "ease:", "state:", "deck:", "front:", "back:"
        ]
        
        for suggestion in field_suggestions:
            if suggestion.startswith(partial_query.lower()):
                suggestions.append(suggestion)
        
        # Add common search terms from card content
        if len(partial_query) >= 2:
            sql = """
            SELECT DISTINCT substr(front, 1, 50) as term
            FROM cards
            WHERE front LIKE ?
            UNION
            SELECT DISTINCT substr(back, 1, 50) as term
            FROM cards
            WHERE back LIKE ?
            LIMIT ?
            """
            
            results = self.db.execute_query(sql, [f"{partial_query}%", f"{partial_query}%", limit])
            for row in results:
                term = row['term'].strip()
                if term and term not in suggestions:
                    suggestions.append(term)
        
        return suggestions[:limit]
