"""
Anki deck importer for the spaced repetition system.

This module provides functionality to import Anki .apkg files
and convert them to the SRS format.
"""

import logging
import sqlite3
import zipfile
import tempfile
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from ..models import Deck, Card
from ..database import get_database

logger = logging.getLogger(__name__)


class AnkiImporter:
    """
    Imports Anki .apkg files into the SRS system.
    
    Handles parsing of Anki database structure, conversion of scheduling data,
    and migration of cards with their review history.
    """
    
    def __init__(self):
        """Initialize the Anki importer."""
        self.temp_dir = None
        self.anki_db = None
        self.media_map = {}
    
    def import_deck(self, apkg_path: str, deck_name: str, 
                   dry_run: bool = False, merge: bool = False) -> Dict[str, Any]:
        """
        Import an Anki deck from .apkg file.
        
        Args:
            apkg_path: Path to the .apkg file
            deck_name: Name for the imported deck
            dry_run: If True, only analyze without importing
            merge: If True, merge with existing deck
            
        Returns:
            Dictionary with import results
        """
        logger.info(f"Starting Anki import: {apkg_path} -> {deck_name}")
        
        try:
            # Extract and analyze the .apkg file
            self._extract_apkg(apkg_path)
            cards_data = self._parse_anki_database()
            
            if dry_run:
                return self._create_dry_run_report(cards_data, deck_name)
            
            # Import the cards
            return self._import_cards(cards_data, deck_name, merge)
            
        except Exception as e:
            logger.error(f"Anki import failed: {e}")
            raise
        finally:
            self._cleanup()
    
    def _extract_apkg(self, apkg_path: str):
        """Extract .apkg file to temporary directory."""
        self.temp_dir = tempfile.mkdtemp()
        
        with zipfile.ZipFile(apkg_path, 'r') as zip_file:
            zip_file.extractall(self.temp_dir)
        
        # Load media mapping
        media_file = Path(self.temp_dir) / 'media'
        if media_file.exists():
            with open(media_file, 'r', encoding='utf-8') as f:
                self.media_map = json.load(f)
        
        logger.debug(f"Extracted .apkg to {self.temp_dir}")
    
    def _parse_anki_database(self) -> List[Dict[str, Any]]:
        """Parse the Anki SQLite database."""
        db_path = Path(self.temp_dir) / 'collection.anki2'
        if not db_path.exists():
            raise ValueError("Invalid .apkg file: missing collection.anki2")
        
        self.anki_db = sqlite3.connect(str(db_path))
        self.anki_db.row_factory = sqlite3.Row
        
        # Get cards and notes
        query = """
        SELECT 
            c.id as card_id,
            c.nid as note_id,
            c.did as deck_id,
            c.ord as card_ord,
            c.mod as modified,
            c.usn as update_sequence,
            c.type as card_type,
            c.queue as queue,
            c.due as due,
            c.ivl as interval,
            c.factor as ease_factor,
            c.reps as repetitions,
            c.lapses as lapses,
            c.left as steps_left,
            c.odue as original_due,
            c.odid as original_deck_id,
            c.flags as flags,
            c.data as data,
            n.flds as fields,
            n.tags as tags,
            n.guid as note_guid,
            n.mid as model_id,
            d.name as deck_name
        FROM cards c
        JOIN notes n ON c.nid = n.id
        JOIN decks d ON c.did = d.id
        ORDER BY c.id
        """
        
        cursor = self.anki_db.execute(query)
        cards_data = []
        
        for row in cursor:
            card_data = {
                'card_id': row['card_id'],
                'note_id': row['note_id'],
                'deck_name': row['deck_name'],
                'fields': row['fields'].split('\x1f'),  # Anki field separator
                'tags': row['tags'].strip().split() if row['tags'] else [],
                'card_type': row['card_type'],
                'queue': row['queue'],
                'due': row['due'],
                'interval': row['interval'],
                'ease_factor': row['ease_factor'] / 1000.0 if row['ease_factor'] else 2.5,
                'repetitions': row['repetitions'],
                'lapses': row['lapses'],
                'modified': datetime.fromtimestamp(row['modified']),
                'card_ord': row['card_ord']
            }
            cards_data.append(card_data)
        
        logger.info(f"Parsed {len(cards_data)} cards from Anki database")
        return cards_data
    
    def _create_dry_run_report(self, cards_data: List[Dict[str, Any]], 
                              deck_name: str) -> Dict[str, Any]:
        """Create a dry run report without importing."""
        total_cards = len(cards_data)
        new_cards = sum(1 for card in cards_data if card['card_type'] == 0)
        learning_cards = sum(1 for card in cards_data if card['card_type'] == 1)
        review_cards = sum(1 for card in cards_data if card['card_type'] == 2)
        
        # Analyze tags
        all_tags = set()
        for card in cards_data:
            all_tags.update(card['tags'])
        
        # Analyze decks
        deck_names = set(card['deck_name'] for card in cards_data)
        
        return {
            'dry_run': True,
            'target_deck': deck_name,
            'total_cards': total_cards,
            'new_cards': new_cards,
            'learning_cards': learning_cards,
            'review_cards': review_cards,
            'unique_tags': len(all_tags),
            'tags': sorted(all_tags),
            'source_decks': sorted(deck_names),
            'estimated_import_time': total_cards * 0.01  # Rough estimate
        }
    
    def _import_cards(self, cards_data: List[Dict[str, Any]], 
                     deck_name: str, merge: bool) -> Dict[str, Any]:
        """Import cards into the SRS system."""
        # Get or create target deck
        deck = Deck.get_by_name(deck_name)
        if deck and not merge:
            raise ValueError(f"Deck '{deck_name}' already exists. Use --merge to merge.")
        elif not deck:
            deck = Deck.create(deck_name)
        
        imported_count = 0
        skipped_count = 0
        error_count = 0
        
        for card_data in cards_data:
            try:
                # Convert Anki card to SRS format
                front, back = self._extract_card_content(card_data)
                
                if not front or not back:
                    skipped_count += 1
                    continue
                
                # Create the card
                card = Card.create(deck.id, front, back)
                
                # Convert scheduling data
                self._convert_scheduling_data(card, card_data)
                
                # Add tags if any
                if card_data['tags']:
                    self._add_tags_to_card(card, card_data['tags'])
                
                card.save()
                imported_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to import card {card_data['card_id']}: {e}")
                error_count += 1
        
        return {
            'dry_run': False,
            'target_deck': deck_name,
            'deck_id': deck.id,
            'imported_cards': imported_count,
            'skipped_cards': skipped_count,
            'error_cards': error_count,
            'total_processed': len(cards_data)
        }
    
    def _extract_card_content(self, card_data: Dict[str, Any]) -> Tuple[str, str]:
        """Extract front and back content from Anki card."""
        fields = card_data['fields']
        
        if len(fields) < 2:
            return "", ""
        
        # Basic extraction - front is first field, back is second
        # This is a simplified approach; real Anki cards can be more complex
        front = self._clean_html(fields[0])
        back = self._clean_html(fields[1])
        
        return front, back
    
    def _clean_html(self, content: str) -> str:
        """Clean HTML content from Anki fields."""
        import re
        
        # Remove HTML tags (basic cleaning)
        content = re.sub(r'<[^>]+>', '', content)
        
        # Replace HTML entities
        content = content.replace('&nbsp;', ' ')
        content = content.replace('&lt;', '<')
        content = content.replace('&gt;', '>')
        content = content.replace('&amp;', '&')
        
        # Clean up whitespace
        content = ' '.join(content.split())
        
        return content.strip()
    
    def _convert_scheduling_data(self, card: Card, anki_data: Dict[str, Any]):
        """Convert Anki scheduling data to SRS format."""
        # Convert ease factor (Anki uses 1000-based, we use decimal)
        card.ease_factor = anki_data['ease_factor']
        
        # Convert interval
        if anki_data['interval'] > 0:
            card.interval = anki_data['interval']
        
        # Convert repetitions
        card.repetitions = anki_data['repetitions']
        
        # Convert due date
        if anki_data['due'] > 0:
            # Anki due dates are days since epoch for review cards
            if anki_data['card_type'] == 2:  # Review card
                epoch = datetime(1970, 1, 1)
                card.due_date = epoch + timedelta(days=anki_data['due'])
            else:
                # For new/learning cards, due is in seconds
                card.due_date = datetime.fromtimestamp(anki_data['due'])
        
        # Set learning state based on Anki card type
        if anki_data['card_type'] == 0:  # New
            card.learning_state = 'new'
        elif anki_data['card_type'] == 1:  # Learning
            card.learning_state = 'learning'
        elif anki_data['card_type'] == 2:  # Review
            card.learning_state = 'graduated'
        elif anki_data['card_type'] == 3:  # Relearning
            card.learning_state = 'relearning'
    
    def _add_tags_to_card(self, card: Card, tags: List[str]):
        """Add tags to the imported card."""
        # This would require the tags system to be implemented
        # For now, we'll skip tag import
        pass
    
    def _cleanup(self):
        """Clean up temporary files."""
        if self.anki_db:
            self.anki_db.close()
        
        if self.temp_dir and os.path.exists(self.temp_dir):
            import shutil
            shutil.rmtree(self.temp_dir)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported import formats."""
        return ['.apkg']
    
    def validate_file(self, file_path: str) -> bool:
        """Validate if file can be imported."""
        if not file_path.endswith('.apkg'):
            return False
        
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                return 'collection.anki2' in zip_file.namelist()
        except:
            return False
