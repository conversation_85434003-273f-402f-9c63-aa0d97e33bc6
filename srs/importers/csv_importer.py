"""
CSV importer for the spaced repetition system.

This module provides functionality to import cards from CSV files
with various formats and configurations.
"""

import logging
import csv
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models import Deck, Card
from ..utils import parse_csv_file

logger = logging.getLogger(__name__)


class CSVImporter:
    """
    Imports cards from CSV files into the SRS system.
    
    Supports various CSV formats and provides flexible column mapping.
    """
    
    def __init__(self):
        """Initialize the CSV importer."""
        pass
    
    def import_deck(self, csv_path: str, deck_name: str, 
                   delimiter: str = ',', dry_run: bool = False,
                   merge: bool = False, column_mapping: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Import cards from CSV file.
        
        Args:
            csv_path: Path to the CSV file
            deck_name: Name for the imported deck
            delimiter: CSV delimiter character
            dry_run: If True, only analyze without importing
            merge: If True, merge with existing deck
            column_mapping: Custom column mapping
            
        Returns:
            Dictionary with import results
        """
        logger.info(f"Starting CSV import: {csv_path} -> {deck_name}")
        
        try:
            # Parse the CSV file
            cards_data = self._parse_csv_file(csv_path, delimiter, column_mapping)
            
            if dry_run:
                return self._create_dry_run_report(cards_data, deck_name)
            
            # Import the cards
            return self._import_cards(cards_data, deck_name, merge)
            
        except Exception as e:
            logger.error(f"CSV import failed: {e}")
            raise
    
    def _parse_csv_file(self, csv_path: str, delimiter: str,
                       column_mapping: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """Parse CSV file and extract card data."""
        cards_data = []
        
        with open(csv_path, 'r', encoding='utf-8') as file:
            # Detect if file has headers
            sample = file.read(1024)
            file.seek(0)

            has_header = False
            try:
                sniffer = csv.Sniffer()
                has_header = sniffer.has_header(sample)
            except:
                # If sniffer fails, assume first row is header if it looks like one
                first_line = sample.split('\n')[0] if sample else ""
                if delimiter in first_line:
                    fields = first_line.split(delimiter)
                    # Heuristic: if fields contain common header words, assume header
                    header_words = ['front', 'back', 'question', 'answer', 'tag', 'difficulty']
                    has_header = any(word.lower() in field.lower() for field in fields for word in header_words)

            reader = csv.reader(file, delimiter=delimiter)
            
            headers = None
            if has_header:
                headers = next(reader)
            
            for row_num, row in enumerate(reader, start=1):
                if len(row) < 2:
                    logger.warning(f"Row {row_num}: Insufficient columns, skipping")
                    continue
                
                card_data = self._extract_card_data(row, headers, column_mapping)
                if card_data:
                    cards_data.append(card_data)
        
        logger.info(f"Parsed {len(cards_data)} cards from CSV file")
        return cards_data
    
    def _extract_card_data(self, row: List[str], headers: Optional[List[str]],
                          column_mapping: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """Extract card data from CSV row."""
        if len(row) < 2:
            return None
        
        # Default mapping
        if column_mapping:
            front_col = column_mapping.get('front', 0)
            back_col = column_mapping.get('back', 1)
            tags_col = column_mapping.get('tags', None)
        elif headers:
            # Try to detect columns by header names
            front_col = self._find_column_by_name(headers, ['front', 'question', 'prompt'])
            back_col = self._find_column_by_name(headers, ['back', 'answer', 'response'])
            tags_col = self._find_column_by_name(headers, ['tags', 'tag', 'categories'])
        else:
            # Default to first two columns
            front_col = 0
            back_col = 1
            tags_col = None
        
        try:
            front = row[front_col].strip() if isinstance(front_col, int) and front_col < len(row) else ""
            back = row[back_col].strip() if isinstance(back_col, int) and back_col < len(row) else ""
            
            if not front or not back:
                return None
            
            tags = []
            if tags_col is not None and isinstance(tags_col, int) and tags_col < len(row):
                tags_str = row[tags_col].strip()
                if tags_str:
                    # Split tags by common separators
                    import re
                    tags = re.split(r'[,;|]', tags_str)
                    tags = [tag.strip() for tag in tags if tag.strip()]
            
            return {
                'front': front,
                'back': back,
                'tags': tags
            }
        except (IndexError, ValueError) as e:
            logger.warning(f"Error extracting card data from row: {e}")
            return None
    
    def _find_column_by_name(self, headers: List[str], possible_names: List[str]) -> Optional[int]:
        """Find column index by matching header names."""
        headers_lower = [h.lower() for h in headers]
        
        for name in possible_names:
            if name.lower() in headers_lower:
                return headers_lower.index(name.lower())
        
        return None
    
    def _create_dry_run_report(self, cards_data: List[Dict[str, Any]], 
                              deck_name: str) -> Dict[str, Any]:
        """Create a dry run report without importing."""
        total_cards = len(cards_data)
        
        # Analyze tags
        all_tags = set()
        for card in cards_data:
            all_tags.update(card.get('tags', []))
        
        # Sample cards for preview
        sample_cards = cards_data[:5] if cards_data else []
        
        return {
            'dry_run': True,
            'target_deck': deck_name,
            'total_cards': total_cards,
            'unique_tags': len(all_tags),
            'tags': sorted(all_tags),
            'sample_cards': sample_cards,
            'estimated_import_time': total_cards * 0.005  # Rough estimate
        }
    
    def _import_cards(self, cards_data: List[Dict[str, Any]], 
                     deck_name: str, merge: bool) -> Dict[str, Any]:
        """Import cards into the SRS system."""
        # Get or create target deck
        deck = Deck.get_by_name(deck_name)
        if deck and not merge:
            raise ValueError(f"Deck '{deck_name}' already exists. Use --merge to merge.")
        elif not deck:
            deck = Deck.create(deck_name)
        
        imported_count = 0
        skipped_count = 0
        error_count = 0
        
        for card_data in cards_data:
            try:
                front = card_data['front']
                back = card_data['back']
                
                if not front or not back:
                    skipped_count += 1
                    continue
                
                # Create the card
                card = Card.create(deck.id, front, back)
                
                # Add tags if any (would require tags system)
                if card_data.get('tags'):
                    self._add_tags_to_card(card, card_data['tags'])
                
                card.save()
                imported_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to import card: {e}")
                error_count += 1
        
        return {
            'dry_run': False,
            'target_deck': deck_name,
            'deck_id': deck.id,
            'imported_cards': imported_count,
            'skipped_cards': skipped_count,
            'error_cards': error_count,
            'total_processed': len(cards_data)
        }
    
    def _add_tags_to_card(self, card: Card, tags: List[str]):
        """Add tags to the imported card."""
        # This would require the tags system to be implemented
        # For now, we'll skip tag import
        pass
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported import formats."""
        return ['.csv', '.tsv', '.txt']
    
    def validate_file(self, file_path: str) -> bool:
        """Validate if file can be imported."""
        if not any(file_path.endswith(ext) for ext in self.get_supported_formats()):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                # Try to read first few lines
                for i, line in enumerate(file):
                    if i >= 5:  # Check first 5 lines
                        break
                    if not line.strip():
                        continue
                    # Basic validation - should have at least one delimiter
                    if ',' in line or '\t' in line or ';' in line:
                        return True
            return False
        except:
            return False
    
    def detect_delimiter(self, file_path: str) -> str:
        """Detect the most likely delimiter in the CSV file."""
        with open(file_path, 'r', encoding='utf-8') as file:
            sample = file.read(1024)

        # Try common delimiters in order of preference
        delimiters = [',', '\t', ';', '|']

        sniffer = csv.Sniffer()
        try:
            delimiter = sniffer.sniff(sample, delimiters=',\t;|').delimiter
            return delimiter
        except:
            # Fallback: count occurrences of each delimiter
            delimiter_counts = {}
            for delim in delimiters:
                delimiter_counts[delim] = sample.count(delim)

            # Return the delimiter with the highest count, or comma if none found
            best_delimiter = max(delimiter_counts, key=delimiter_counts.get)
            return best_delimiter if delimiter_counts[best_delimiter] > 0 else ','
    
    def preview_file(self, file_path: str, delimiter: str = None, 
                    lines: int = 5) -> Dict[str, Any]:
        """Preview the first few lines of a CSV file."""
        if delimiter is None:
            delimiter = self.detect_delimiter(file_path)
        
        preview_data = []
        headers = None
        
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=delimiter)
            
            # Check for headers
            sample = file.read(1024)
            file.seek(0)
            
            sniffer = csv.Sniffer()
            has_header = sniffer.has_header(sample)
            
            if has_header:
                headers = next(reader)
            
            for i, row in enumerate(reader):
                if i >= lines:
                    break
                preview_data.append(row)
        
        return {
            'delimiter': delimiter,
            'has_header': has_header,
            'headers': headers,
            'preview_rows': preview_data,
            'estimated_columns': len(preview_data[0]) if preview_data else 0
        }
