"""
Markdown importer for the spaced repetition system.

This module provides functionality to import cards from Markdown files
with structured format for better integration with note-taking systems.
"""

import logging
import re
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models import Deck, Card

logger = logging.getLogger(__name__)


class MarkdownImporter:
    """
    Imports cards from Markdown files into the SRS system.
    
    Supports structured Markdown format with card metadata.
    """
    
    def __init__(self):
        """Initialize the Markdown importer."""
        pass
    
    def import_deck(self, markdown_path: str, deck_name: str = None, 
                   dry_run: bool = False, merge: bool = False) -> Dict[str, Any]:
        """
        Import cards from Markdown file.
        
        Args:
            markdown_path: Path to the Markdown file
            deck_name: Name for the imported deck (optional, can be extracted from file)
            dry_run: If True, only analyze without importing
            merge: If True, merge with existing deck
            
        Returns:
            Dictionary with import results
        """
        logger.info(f"Starting Markdown import: {markdown_path}")
        
        try:
            # Parse the Markdown file
            deck_info, cards_data = self._parse_markdown_file(markdown_path)
            
            # Use deck name from file if not provided
            if not deck_name:
                deck_name = deck_info.get('name', Path(markdown_path).stem)
            
            if dry_run:
                return self._create_dry_run_report(cards_data, deck_name, deck_info)
            
            # Import the cards
            return self._import_cards(cards_data, deck_name, merge, deck_info)
            
        except Exception as e:
            logger.error(f"Markdown import failed: {e}")
            raise
    
    def _parse_markdown_file(self, markdown_path: str) -> tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Parse Markdown file and extract deck info and card data."""
        with open(markdown_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Extract deck information
        deck_info = self._extract_deck_info(content)
        
        # Extract cards
        cards_data = self._extract_cards(content)
        
        logger.info(f"Parsed {len(cards_data)} cards from Markdown file")
        return deck_info, cards_data
    
    def _extract_deck_info(self, content: str) -> Dict[str, Any]:
        """Extract deck information from Markdown content."""
        deck_info = {}
        
        # Look for deck name in h1 header
        deck_match = re.search(r'^#\s+Deck:\s*(.+)$', content, re.MULTILINE)
        if deck_match:
            deck_info['name'] = deck_match.group(1).strip()
        
        # Look for deck description
        desc_match = re.search(r'^>\s*(.+)$', content, re.MULTILINE)
        if desc_match:
            deck_info['description'] = desc_match.group(1).strip()
        
        # Look for deck settings
        settings_match = re.search(r'```yaml\s*\n(.*?)\n```', content, re.DOTALL)
        if settings_match:
            try:
                import yaml
                settings = yaml.safe_load(settings_match.group(1))
                deck_info['settings'] = settings
            except:
                # If yaml not available or invalid, skip settings
                pass
        
        return deck_info
    
    def _extract_cards(self, content: str) -> List[Dict[str, Any]]:
        """Extract cards from Markdown content."""
        cards_data = []
        
        # Split content by card headers (## Card N or ## followed by any text)
        card_sections = re.split(r'^##\s+.*$', content, flags=re.MULTILINE)[1:]
        
        for i, section in enumerate(card_sections):
            card_data = self._parse_card_section(section, i + 1)
            if card_data:
                cards_data.append(card_data)
        
        return cards_data
    
    def _parse_card_section(self, section: str, card_number: int) -> Optional[Dict[str, Any]]:
        """Parse a single card section."""
        card_data = {
            'front': '',
            'back': '',
            'tags': [],
            'difficulty': 0.5,
            'notes': ''
        }
        
        # Extract front content
        front_match = re.search(r'\*\*Front:\*\*\s*(.+?)(?=\*\*|$)', section, re.DOTALL)
        if front_match:
            card_data['front'] = front_match.group(1).strip()
        
        # Extract back content
        back_match = re.search(r'\*\*Back:\*\*\s*(.+?)(?=\*\*|$)', section, re.DOTALL)
        if back_match:
            card_data['back'] = back_match.group(1).strip()
        
        # Extract tags
        tags_match = re.search(r'\*\*Tags:\*\*\s*(.+?)(?=\*\*|$)', section, re.DOTALL)
        if tags_match:
            tags_str = tags_match.group(1).strip()
            # Split by comma, semicolon, or space
            tags = re.split(r'[,;\s]+', tags_str)
            card_data['tags'] = [tag.strip() for tag in tags if tag.strip()]
        
        # Extract difficulty
        diff_match = re.search(r'\*\*Difficulty:\*\*\s*([0-9.]+)', section)
        if diff_match:
            try:
                card_data['difficulty'] = float(diff_match.group(1))
            except ValueError:
                pass
        
        # Extract notes
        notes_match = re.search(r'\*\*Notes:\*\*\s*(.+?)(?=\*\*|$)', section, re.DOTALL)
        if notes_match:
            card_data['notes'] = notes_match.group(1).strip()
        
        # Validate required fields
        if not card_data['front'] or not card_data['back']:
            logger.warning(f"Card {card_number}: Missing front or back content")
            return None
        
        return card_data
    
    def _create_dry_run_report(self, cards_data: List[Dict[str, Any]], 
                              deck_name: str, deck_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create a dry run report without importing."""
        total_cards = len(cards_data)
        
        # Analyze tags
        all_tags = set()
        for card in cards_data:
            all_tags.update(card.get('tags', []))
        
        # Analyze difficulty distribution
        difficulties = [card.get('difficulty', 0.5) for card in cards_data]
        avg_difficulty = sum(difficulties) / len(difficulties) if difficulties else 0.5
        
        # Sample cards for preview
        sample_cards = cards_data[:3] if cards_data else []
        
        return {
            'dry_run': True,
            'target_deck': deck_name,
            'deck_info': deck_info,
            'total_cards': total_cards,
            'unique_tags': len(all_tags),
            'tags': sorted(all_tags),
            'average_difficulty': avg_difficulty,
            'sample_cards': sample_cards,
            'estimated_import_time': total_cards * 0.01
        }
    
    def _import_cards(self, cards_data: List[Dict[str, Any]], 
                     deck_name: str, merge: bool, deck_info: Dict[str, Any]) -> Dict[str, Any]:
        """Import cards into the SRS system."""
        # Get or create target deck
        deck = Deck.get_by_name(deck_name)
        if deck and not merge:
            raise ValueError(f"Deck '{deck_name}' already exists. Use --merge to merge.")
        elif not deck:
            deck = Deck.create(deck_name)
            
            # Apply deck settings if available
            if 'description' in deck_info:
                deck.description = deck_info['description']
            
            if 'settings' in deck_info:
                settings = deck_info['settings']
                if 'daily_new_limit' in settings:
                    deck.daily_new_limit = settings['daily_new_limit']
                if 'daily_review_limit' in settings:
                    deck.daily_review_limit = settings['daily_review_limit']
                if 'learning_steps' in settings:
                    deck.learning_steps = ' '.join(map(str, settings['learning_steps']))
            
            deck.save()
        
        imported_count = 0
        skipped_count = 0
        error_count = 0
        
        for card_data in cards_data:
            try:
                front = card_data['front']
                back = card_data['back']
                
                if not front or not back:
                    skipped_count += 1
                    continue
                
                # Create the card
                card = Card.create(deck.id, front, back)
                
                # Set difficulty if provided
                if 'difficulty' in card_data and card_data['difficulty'] != 0.5:
                    card.difficulty = card_data['difficulty']
                
                # Add tags if any (would require tags system)
                if card_data.get('tags'):
                    self._add_tags_to_card(card, card_data['tags'])
                
                card.save()
                imported_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to import card: {e}")
                error_count += 1
        
        return {
            'dry_run': False,
            'target_deck': deck_name,
            'deck_id': deck.id,
            'imported_cards': imported_count,
            'skipped_cards': skipped_count,
            'error_cards': error_count,
            'total_processed': len(cards_data)
        }
    
    def _add_tags_to_card(self, card: Card, tags: List[str]):
        """Add tags to the imported card."""
        # This would require the tags system to be implemented
        # For now, we'll skip tag import
        pass
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported import formats."""
        return ['.md', '.markdown']
    
    def validate_file(self, file_path: str) -> bool:
        """Validate if file can be imported."""
        if not any(file_path.endswith(ext) for ext in self.get_supported_formats()):
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                # Basic validation - should have at least one card section
                return bool(re.search(r'^##\s+', content, re.MULTILINE))
        except:
            return False
    
    def preview_file(self, file_path: str, lines: int = 10) -> Dict[str, Any]:
        """Preview the first few lines of a Markdown file."""
        preview_lines = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for i, line in enumerate(file):
                    if i >= lines:
                        break
                    preview_lines.append(line.rstrip())
            
            # Try to extract basic info
            content = '\n'.join(preview_lines)
            deck_info = self._extract_deck_info(content)
            
            return {
                'preview_lines': preview_lines,
                'deck_info': deck_info,
                'estimated_cards': len(re.findall(r'^##\s+', content, re.MULTILINE))
            }
        except Exception as e:
            return {
                'error': str(e),
                'preview_lines': [],
                'deck_info': {},
                'estimated_cards': 0
            }
