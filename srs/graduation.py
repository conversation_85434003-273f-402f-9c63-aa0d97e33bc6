"""
Card graduation system for the spaced repetition system.

This module handles the learning process for new cards, including
learning steps, graduation criteria, and relearning logic.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from enum import Enum

from .models import Card, Deck
from .algorithm import get_algorithm

logger = logging.getLogger(__name__)


class LearningResponse(Enum):
    """Response options for learning cards."""
    AGAIN = 1      # Restart learning
    HARD = 2       # Repeat current step
    GOOD = 3       # Advance to next step
    EASY = 4       # Graduate immediately


class GraduationEngine:
    """
    Handles card graduation and learning progression.
    
    Manages the transition of cards through learning steps,
    graduation criteria, and relearning processes.
    """
    
    def __init__(self):
        """Initialize the graduation engine."""
        self.algorithm = get_algorithm()
    
    def process_learning_response(self, card: Card, response: LearningResponse, 
                                review_duration: int = 0) -> Dict[str, Any]:
        """
        Process a learning response and update card state.
        
        Args:
            card: Card being reviewed
            response: Learning response (Again, Hard, Good, Easy)
            review_duration: Time taken to review in seconds
            
        Returns:
            Dictionary with update information
        """
        logger.debug(f"Processing learning response {response.name} for card {card.id}")
        
        # Update difficulty and performance tracking
        card.update_difficulty(response.value, review_duration)
        
        # Get deck configuration
        deck = Deck.get_by_id(card.deck_id)
        if not deck:
            raise ValueError(f"Deck not found for card {card.id}")
        
        result = {
            'card_id': card.id,
            'previous_state': card.learning_state,
            'previous_step': card.current_step,
            'response': response.name,
            'graduated': False,
            'next_due': None,
            'message': ''
        }
        
        if response == LearningResponse.AGAIN:
            # Restart learning
            self._handle_again_response(card, result)
        elif response == LearningResponse.HARD:
            # Repeat current step
            self._handle_hard_response(card, result)
        elif response == LearningResponse.GOOD:
            # Advance to next step or graduate
            self._handle_good_response(card, deck, result)
        elif response == LearningResponse.EASY:
            # Graduate immediately with easy interval
            self._handle_easy_response(card, deck, result)
        
        # Save card changes
        card.save()
        
        result['new_state'] = card.learning_state
        result['new_step'] = card.current_step
        result['next_due'] = card.step_due_date or card.due_date
        
        logger.info(f"Card {card.id} learning response processed: {result['message']}")
        return result
    
    def _handle_again_response(self, card: Card, result: Dict[str, Any]):
        """Handle 'Again' response - restart learning."""
        card.fail_learning_step()
        result['message'] = 'Learning restarted'
    
    def _handle_hard_response(self, card: Card, result: Dict[str, Any]):
        """Handle 'Hard' response - repeat current step."""
        if card.learning_state == 'new':
            # Start learning
            card.learning_state = 'learning'
            card.current_step = 0
            steps = card.get_learning_steps()
            if steps:
                step_minutes = steps[0]
                card.step_due_date = datetime.now() + timedelta(minutes=step_minutes)
        else:
            # Repeat current step
            steps = card.get_learning_steps() if card.learning_state == 'learning' else card.get_relearning_steps()
            if card.current_step < len(steps):
                step_minutes = steps[card.current_step]
                card.step_due_date = datetime.now() + timedelta(minutes=step_minutes)
        
        result['message'] = 'Repeating current learning step'
    
    def _handle_good_response(self, card: Card, deck: Deck, result: Dict[str, Any]):
        """Handle 'Good' response - advance to next step or graduate."""
        if card.learning_state == 'new':
            # Start learning
            card.learning_state = 'learning'
            card.current_step = 0
            steps = card.get_learning_steps()
            if steps:
                step_minutes = steps[0]
                card.step_due_date = datetime.now() + timedelta(minutes=step_minutes)
            result['message'] = 'Started learning'
        else:
            # Try to advance
            graduated = not card.advance_learning_step()
            if graduated:
                result['graduated'] = True
                result['message'] = 'Card graduated!'
            else:
                result['message'] = 'Advanced to next learning step'
    
    def _handle_easy_response(self, card: Card, deck: Deck, result: Dict[str, Any]):
        """Handle 'Easy' response - graduate immediately with easy interval."""
        card.learning_state = 'graduated'
        card.current_step = 0
        card.step_due_date = None
        card.interval = deck.easy_interval
        card.due_date = datetime.now() + timedelta(days=deck.easy_interval)
        card.repetitions = 1
        
        result['graduated'] = True
        result['message'] = f'Card graduated with easy interval ({deck.easy_interval} days)'
    
    def get_learning_buttons(self, card: Card) -> List[Dict[str, Any]]:
        """
        Get available learning response buttons for a card.
        
        Args:
            card: Card being reviewed
            
        Returns:
            List of button configurations
        """
        if not card.is_learning():
            # Not a learning card, return empty list
            return []
        
        buttons = []
        
        # Again button
        buttons.append({
            'response': LearningResponse.AGAIN,
            'label': 'Again',
            'description': 'Restart learning',
            'shortcut': '1',
            'color': 'red'
        })
        
        # Hard button
        buttons.append({
            'response': LearningResponse.HARD,
            'label': 'Hard',
            'description': 'Repeat step',
            'shortcut': '2',
            'color': 'orange'
        })
        
        # Good button
        next_interval = card.get_next_step_interval()
        if next_interval:
            good_desc = f'Next step ({next_interval}m)'
        else:
            deck = Deck.get_by_id(card.deck_id)
            grad_interval = deck.graduation_interval if deck else 1
            good_desc = f'Graduate ({grad_interval}d)'
        
        buttons.append({
            'response': LearningResponse.GOOD,
            'label': 'Good',
            'description': good_desc,
            'shortcut': '3',
            'color': 'green'
        })
        
        # Easy button
        deck = Deck.get_by_id(card.deck_id)
        easy_interval = deck.easy_interval if deck else 4
        buttons.append({
            'response': LearningResponse.EASY,
            'label': 'Easy',
            'description': f'Graduate easy ({easy_interval}d)',
            'shortcut': '4',
            'color': 'blue'
        })
        
        return buttons
    
    def get_learning_progress(self, card: Card) -> Dict[str, Any]:
        """
        Get learning progress information for a card.
        
        Args:
            card: Card to get progress for
            
        Returns:
            Dictionary with progress information
        """
        if not card.is_learning():
            return {
                'is_learning': False,
                'state': card.learning_state,
                'progress': 1.0,
                'steps_completed': 0,
                'total_steps': 0,
                'current_step_interval': None,
                'next_step_interval': None
            }
        
        if card.learning_state in ['new', 'learning']:
            steps = card.get_learning_steps()
        else:  # relearning
            steps = card.get_relearning_steps()
        
        total_steps = len(steps)
        steps_completed = card.current_step + (1 if card.learning_state != 'new' else 0)
        progress = steps_completed / max(1, total_steps)
        
        current_interval = steps[card.current_step] if card.current_step < len(steps) else None
        next_interval = card.get_next_step_interval()
        
        return {
            'is_learning': True,
            'state': card.learning_state,
            'progress': progress,
            'steps_completed': steps_completed,
            'total_steps': total_steps,
            'current_step_interval': current_interval,
            'next_step_interval': next_interval,
            'step_due_date': card.step_due_date
        }
    
    def get_cards_due_for_learning(self, deck_id: Optional[int] = None) -> List[Card]:
        """
        Get cards that are due for learning steps.
        
        Args:
            deck_id: Optional deck ID to filter by
            
        Returns:
            List of cards due for learning
        """
        from .database import get_database
        
        db = get_database()
        now = datetime.now()
        
        # Query for learning cards that are due
        if deck_id:
            query = """
            SELECT * FROM cards 
            WHERE deck_id = ? AND learning_state IN ('new', 'learning', 'relearning')
            AND (step_due_date IS NULL OR step_due_date <= ?)
            ORDER BY step_due_date ASC, created_at ASC
            """
            params = (deck_id, now.isoformat())
        else:
            query = """
            SELECT * FROM cards 
            WHERE learning_state IN ('new', 'learning', 'relearning')
            AND (step_due_date IS NULL OR step_due_date <= ?)
            ORDER BY step_due_date ASC, created_at ASC
            """
            params = (now.isoformat(),)
        
        results = db.execute_query(query, params)
        
        cards = []
        for row in results:
            card = Card(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                due_date=datetime.fromisoformat(row['due_date']),
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] if 'difficulty' in row.keys() else 0.5,
                last_review_duration=row['last_review_duration'] if 'last_review_duration' in row.keys() else 0,
                consecutive_correct=row['consecutive_correct'] if 'consecutive_correct' in row.keys() else 0,
                consecutive_incorrect=row['consecutive_incorrect'] if 'consecutive_incorrect' in row.keys() else 0,
                learning_state=row['learning_state'] if 'learning_state' in row.keys() else 'new',
                current_step=row['current_step'] if 'current_step' in row.keys() else 0,
                step_due_date=datetime.fromisoformat(row['step_due_date']) if row.get('step_due_date') else None
            )
            cards.append(card)
        
        return cards
    
    def get_learning_statistics(self, deck_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get learning statistics for cards.
        
        Args:
            deck_id: Optional deck ID to filter by
            
        Returns:
            Dictionary with learning statistics
        """
        from .database import get_database
        
        db = get_database()
        
        if deck_id:
            where_clause = "WHERE deck_id = ?"
            params = (deck_id,)
        else:
            where_clause = ""
            params = ()
        
        # Get counts by learning state
        query = f"""
        SELECT learning_state, COUNT(*) as count
        FROM cards {where_clause}
        GROUP BY learning_state
        """
        
        results = db.execute_query(query, params)
        
        stats = {
            'new': 0,
            'learning': 0,
            'graduated': 0,
            'relearning': 0,
            'total': 0
        }
        
        for row in results:
            state = row['learning_state'] or 'new'
            count = row['count']
            if state in stats:
                stats[state] = count
            stats['total'] += count
        
        # Calculate percentages
        total = stats['total']
        if total > 0:
            stats['new_percentage'] = (stats['new'] / total) * 100
            stats['learning_percentage'] = (stats['learning'] / total) * 100
            stats['graduated_percentage'] = (stats['graduated'] / total) * 100
            stats['relearning_percentage'] = (stats['relearning'] / total) * 100
        else:
            stats['new_percentage'] = 0
            stats['learning_percentage'] = 0
            stats['graduated_percentage'] = 0
            stats['relearning_percentage'] = 0
        
        return stats
