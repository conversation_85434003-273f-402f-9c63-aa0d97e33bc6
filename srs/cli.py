"""
Command-line interface for the spaced repetition system.

This module implements the CLI commands for deck management, card management,
review sessions, and system utilities.
"""

import sys
import argparse
import logging
import os
import signal
from typing import Optional
from pathlib import Path

from . import initialize_srs, __version__
from .models import Deck, Card, Review
from .review import (
    create_review_session, create_smart_review_session, create_time_boxed_session,
    create_filtered_review_session, ReviewFilter,
    create_hard_cards_session, create_easy_cards_session,
    create_new_cards_session, create_overdue_cards_session, create_learning_cards_session
)
from .scheduling import SessionLimits
from .importers import AnkiImporter, CSVImporter
from .importers.markdown_importer import MarkdownImporter
from .exporters import MarkdownExporter, CSVExporter
from .search import SearchEngine
from .statistics import StatisticsEngine
from .progress import ProgressTracker
from .visualization import (
    create_progress_bar, create_horizontal_bar_chart, create_line_chart,
    create_summary_box, create_achievement_display, export_data_to_csv,
    format_percentage
)
from .utils import (
    validate_deck_name, validate_card_content, parse_csv_file,
    format_card_counts, format_duration, truncate_text
)

logger = logging.getLogger(__name__)


# UI Helper Functions

def print_header(title: str, width: int = 60) -> None:
    """Print a formatted header."""
    print()
    print("=" * width)
    print(f" {title} ".center(width))
    print("=" * width)
    print()


def print_separator(width: int = 60) -> None:
    """Print a separator line."""
    print("-" * width)


def print_success(message: str) -> None:
    """Print a success message."""
    print(f"✅ {message}")


def print_error(message: str) -> None:
    """Print an error message."""
    print(f"❌ {message}")


def print_warning(message: str) -> None:
    """Print a warning message."""
    print(f"⚠️  {message}")


def print_info(message: str) -> None:
    """Print an info message."""
    print(f"ℹ️  {message}")


def confirm_action(message: str, default: bool = False) -> bool:
    """Ask user for confirmation."""
    suffix = " [y/N]" if not default else " [Y/n]"
    try:
        response = input(f"{message}{suffix}: ").strip().lower()
        if not response:
            return default
        return response in ['y', 'yes']
    except (EOFError, KeyboardInterrupt):
        print()
        return False


def get_user_input(prompt: str, required: bool = True) -> Optional[str]:
    """Get user input with optional validation."""
    try:
        while True:
            value = input(f"{prompt}: ").strip()
            if value or not required:
                return value if value else None
            print_error("This field is required. Please enter a value.")
    except (EOFError, KeyboardInterrupt):
        print()
        return None


def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        del signum, frame  # Unused parameters
        print("\n\nOperation cancelled by user.")
        sys.exit(1)

    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)


class CLIError(Exception):
    """Custom exception for CLI errors."""
    pass


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up the main argument parser with all commands."""
    parser = argparse.ArgumentParser(
        prog='srs',
        description='Spaced Repetition System - A terminal-based flashcard application',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  srs create-deck "Spanish Verbs"
  srs add-card "Spanish Verbs"
  srs import-cards "Spanish Verbs" cards.csv
  srs review "Spanish Verbs"
  srs status
        """
    )

    parser.add_argument(
        '--version',
        action='version',
        version=f'SRS {__version__}'
    )

    parser.add_argument(
        '--config',
        help='Path to configuration file',
        metavar='PATH'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    # Create subparsers for commands
    subparsers = parser.add_subparsers(
        dest='command',
        help='Available commands',
        metavar='COMMAND'
    )

    # Deck management commands
    _add_deck_commands(subparsers)

    # Card management commands
    _add_card_commands(subparsers)

    # Review commands
    _add_review_commands(subparsers)

    # Utility commands
    _add_utility_commands(subparsers)

    return parser


def _add_deck_commands(subparsers):
    """Add deck management commands."""
    # create-deck command
    create_deck_parser = subparsers.add_parser(
        'create-deck',
        help='Create a new deck',
        description='Create a new flashcard deck with the specified name.'
    )
    create_deck_parser.add_argument(
        'name',
        help='Name of the deck to create'
    )

    # list-decks command
    list_decks_parser = subparsers.add_parser(
        'list-decks',
        help='List all decks',
        description='Show all decks with card counts and statistics.'
    )
    list_decks_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed information for each deck'
    )

    # delete-deck command
    delete_deck_parser = subparsers.add_parser(
        'delete-deck',
        help='Delete a deck and all its cards',
        description='Delete a deck and all its cards. This action cannot be undone.'
    )
    delete_deck_parser.add_argument(
        'name',
        help='Name of the deck to delete'
    )
    delete_deck_parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Skip confirmation prompt'
    )


def _add_card_commands(subparsers):
    """Add card management commands."""
    # add-card command
    add_card_parser = subparsers.add_parser(
        'add-card',
        help='Add a card to a deck',
        description='Interactively add a new flashcard to the specified deck.'
    )
    add_card_parser.add_argument(
        'deck',
        help='Name of the deck to add the card to'
    )
    add_card_parser.add_argument(
        '--front',
        help='Front side of the card (if not provided, will prompt)'
    )
    add_card_parser.add_argument(
        '--back',
        help='Back side of the card (if not provided, will prompt)'
    )

    # import-cards command
    import_cards_parser = subparsers.add_parser(
        'import-cards',
        help='Import cards from a CSV/TSV file',
        description='Import multiple cards from a CSV or TSV file.'
    )
    import_cards_parser.add_argument(
        'deck',
        help='Name of the deck to import cards into'
    )
    import_cards_parser.add_argument(
        'file',
        help='Path to CSV/TSV file containing cards'
    )
    import_cards_parser.add_argument(
        '--delimiter',
        default=',',
        help='Field delimiter (default: comma)'
    )
    import_cards_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be imported without actually importing'
    )

    # import-anki command
    import_anki_parser = subparsers.add_parser(
        'import-anki',
        help='Import Anki deck from .apkg file',
        description='Import cards from an Anki .apkg file.'
    )
    import_anki_parser.add_argument(
        'deck',
        help='Name of the deck to create or import into'
    )
    import_anki_parser.add_argument(
        'file',
        help='Path to Anki .apkg file'
    )
    import_anki_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview import without actually importing'
    )
    import_anki_parser.add_argument(
        '--merge',
        action='store_true',
        help='Merge with existing deck if it exists'
    )

    # import-markdown command
    import_md_parser = subparsers.add_parser(
        'import-markdown',
        help='Import cards from Markdown file',
        description='Import cards from a structured Markdown file.'
    )
    import_md_parser.add_argument(
        'file',
        help='Path to Markdown file'
    )
    import_md_parser.add_argument(
        'deck',
        nargs='?',
        help='Name of the deck to create (optional, can be extracted from file)'
    )
    import_md_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview import without actually importing'
    )
    import_md_parser.add_argument(
        '--merge',
        action='store_true',
        help='Merge with existing deck if it exists'
    )

    # export-markdown command
    export_md_parser = subparsers.add_parser(
        'export-markdown',
        help='Export deck to Markdown file',
        description='Export cards to a structured Markdown file.'
    )
    export_md_parser.add_argument(
        'deck',
        help='Name of the deck to export'
    )
    export_md_parser.add_argument(
        'file',
        help='Path for output Markdown file'
    )
    export_md_parser.add_argument(
        '--no-metadata',
        action='store_true',
        help='Exclude card metadata (difficulty, learning state)'
    )
    export_md_parser.add_argument(
        '--include-stats',
        action='store_true',
        help='Include review statistics'
    )

    # export-csv command
    export_csv_parser = subparsers.add_parser(
        'export-csv',
        help='Export deck to CSV file',
        description='Export cards to a CSV file with optional scheduling data.'
    )
    export_csv_parser.add_argument(
        'deck',
        help='Name of the deck to export'
    )
    export_csv_parser.add_argument(
        'file',
        help='Path for output CSV file'
    )
    export_csv_parser.add_argument(
        '--delimiter', '-d',
        default=',',
        help='CSV delimiter (default: comma)'
    )
    export_csv_parser.add_argument(
        '--full',
        action='store_true',
        help='Include all data (metadata and statistics)'
    )
    export_csv_parser.add_argument(
        '--scheduling',
        action='store_true',
        help='Include scheduling data only'
    )
    export_csv_parser.add_argument(
        '--no-metadata',
        action='store_true',
        help='Exclude card metadata'
    )

    # search command
    search_parser = subparsers.add_parser(
        'search',
        help='Search for cards',
        description='Search for cards using text queries and filters.'
    )
    search_parser.add_argument(
        'query',
        help='Search query (supports field:value filters, quotes, AND/OR/NOT)'
    )
    search_parser.add_argument(
        '--deck', '-d',
        help='Limit search to specific deck'
    )
    search_parser.add_argument(
        '--limit', '-l',
        type=int,
        default=20,
        help='Maximum number of results (default: 20)'
    )
    search_parser.add_argument(
        '--offset',
        type=int,
        default=0,
        help='Result offset for pagination (default: 0)'
    )
    search_parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show detailed match information'
    )

    # list-cards command
    list_cards_parser = subparsers.add_parser(
        'list-cards',
        help='List cards in a deck',
        description='Show all cards in the specified deck.'
    )
    list_cards_parser.add_argument(
        'deck',
        help='Name of the deck to list cards from'
    )
    list_cards_parser.add_argument(
        '--limit', '-l',
        type=int,
        default=20,
        help='Maximum number of cards to show (default: 20)'
    )
    list_cards_parser.add_argument(
        '--due-only',
        action='store_true',
        help='Show only cards that are due for review'
    )


def _add_review_commands(subparsers):
    """Add review commands."""
    # review command
    review_parser = subparsers.add_parser(
        'review',
        help='Start a review session',
        description='Start an interactive review session for the specified deck.'
    )
    review_parser.add_argument(
        'deck',
        help='Name of the deck to review'
    )
    review_parser.add_argument(
        '--limit', '-l',
        type=int,
        help='Maximum number of cards to review'
    )
    review_parser.add_argument(
        '--time', '-t',
        type=int,
        help='Time limit for session in minutes'
    )
    review_parser.add_argument(
        '--new-only',
        action='store_true',
        help='Review only new cards'
    )
    review_parser.add_argument(
        '--due-only',
        action='store_true',
        help='Review only due cards (excluding new cards)'
    )
    review_parser.add_argument(
        '--smart',
        action='store_true',
        help='Use smart scheduling for card selection'
    )
    review_parser.add_argument(
        '--hard',
        action='store_true',
        help='Review only difficult cards (difficulty > 0.7)'
    )
    review_parser.add_argument(
        '--easy',
        action='store_true',
        help='Review only easy cards (difficulty < 0.3)'
    )
    review_parser.add_argument(
        '--overdue',
        action='store_true',
        help='Review only overdue cards'
    )
    review_parser.add_argument(
        '--learning',
        action='store_true',
        help='Review only learning cards'
    )
    review_parser.add_argument(
        '--difficulty',
        help='Filter by difficulty range (e.g., ">0.5", "<0.3", "0.3-0.7")'
    )
    review_parser.add_argument(
        '--interval',
        help='Filter by interval range (e.g., ">7", "<3", "1-14")'
    )
    review_parser.add_argument(
        '--tag',
        action='append',
        help='Filter by tag (can be used multiple times)'
    )


def _add_utility_commands(subparsers):
    """Add utility commands."""
    # status command
    status_parser = subparsers.add_parser(
        'status',
        help='Show system status',
        description='Show overview of all decks and due cards.'
    )
    status_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed statistics'
    )

    # stats command
    stats_parser = subparsers.add_parser(
        'stats',
        help='Show detailed statistics and analytics',
        description='Display comprehensive statistics, analytics, and progress tracking.'
    )
    stats_parser.add_argument(
        'deck',
        nargs='?',
        help='Show statistics for specific deck (optional)'
    )
    stats_parser.add_argument(
        '--period',
        choices=['week', 'month', 'year', 'all'],
        default='month',
        help='Time period for statistics (default: month)'
    )
    stats_parser.add_argument(
        '--retention',
        action='store_true',
        help='Show retention rate analysis'
    )
    stats_parser.add_argument(
        '--forecast',
        action='store_true',
        help='Show upcoming review forecast'
    )
    stats_parser.add_argument(
        '--performance',
        action='store_true',
        help='Show performance trends'
    )
    stats_parser.add_argument(
        '--export',
        choices=['csv', 'json'],
        help='Export statistics to file'
    )
    stats_parser.add_argument(
        '--output', '-o',
        help='Output file path for export'
    )


# Command implementation functions

def cmd_create_deck(args) -> int:
    """Create a new deck."""
    try:
        # Validate deck name
        is_valid, error_msg = validate_deck_name(args.name)
        if not is_valid:
            print_error(error_msg)
            return 1

        # Check for existing deck
        existing_deck = Deck.get_by_name(args.name)
        if existing_deck:
            print_error(f"Deck '{args.name}' already exists")
            return 1

        # Create the deck
        deck = Deck.create(args.name)
        print_success(f"Created deck '{deck.name}'")
        print_info(f"You can now add cards with: srs add-card \"{deck.name}\"")
        return 0

    except ValueError as e:
        print_error(str(e))
        return 1
    except Exception as e:
        logger.error(f"Unexpected error creating deck: {e}")
        print_error(f"Failed to create deck '{args.name}'")
        return 1


def cmd_list_decks(args) -> int:
    """List all decks."""
    try:
        decks = Deck.get_all()

        if not decks:
            print_header("Your Decks")
            print_info("No decks found")
            print("Get started by creating a deck:")
            print("  srs create-deck \"My First Deck\"")
            print()
            return 0

        print_header(f"Your Decks ({len(decks)} total)")

        total_cards = 0
        total_due = 0
        total_new = 0

        for i, deck in enumerate(decks, 1):
            counts = deck.get_card_counts()
            count_str = format_card_counts(counts)

            total_cards += counts['total']
            total_due += counts['due']
            total_new += counts['new']

            if args.detailed:
                print(f"{i:2d}. 📚 {deck.name}")
                print(f"     Cards: {count_str}")
                print(f"     Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
                if counts['due'] > 0:
                    print(f"     📅 {counts['due']} cards ready for review")
                print()
            else:
                status_icon = "🔥" if counts['due'] > 0 else "📚"
                print(f"{i:2d}. {status_icon} {deck.name:<30} {count_str}")

        if not args.detailed:
            print()

        print_separator()
        print(f"Total: {total_cards} cards, {total_due} due, {total_new} new")

        if total_due > 0:
            print()
            print_info(f"You have {total_due} cards ready for review!")
            print("Start reviewing with: srs review \"<deck-name>\"")

        return 0

    except Exception as e:
        logger.error(f"Error listing decks: {e}")
        print_error("Failed to list decks")
        return 1


def cmd_delete_deck(args) -> int:
    """Delete a deck and all its cards."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.name)
        if not deck:
            print_error(f"Deck '{args.name}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Get card count for confirmation
        counts = deck.get_card_counts()

        # Show deck information
        print_header(f"Delete Deck: {deck.name}")
        print(f"📚 Deck: {deck.name}")
        print(f"📊 Cards: {format_card_counts(counts)}")
        print(f"📅 Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
        print()

        # Confirm deletion unless --force is used
        if not args.force:
            print_warning("This action cannot be undone!")
            if counts['total'] > 0:
                print(f"This will permanently delete {counts['total']} cards.")

            confirmed = confirm_action(
                f"Are you sure you want to delete deck '{deck.name}'?",
                default=False
            )

            if not confirmed:
                print_info("Deletion cancelled")
                return 0

        # Delete the deck
        if deck.delete():
            print_success(f"Deleted deck '{args.name}' and {counts['total']} cards")
            return 0
        else:
            print_error(f"Failed to delete deck '{args.name}'")
            return 1

    except Exception as e:
        logger.error(f"Error deleting deck: {e}")
        print_error(f"Failed to delete deck '{args.name}'")
        return 1


def cmd_add_card(args) -> int:
    """Add a card to a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Get card content
        if args.front and args.back:
            front = args.front
            back = args.back
        else:
            print_header(f"Add Card to: {deck.name}")
            print("Enter the card content (press Ctrl+C to cancel):")
            print()

            front = get_user_input("Front side", required=True)
            if front is None:
                print_info("Card creation cancelled")
                return 0

            back = get_user_input("Back side", required=True)
            if back is None:
                print_info("Card creation cancelled")
                return 0

        # Validate card content
        is_valid, error_msg = validate_card_content(front, back)
        if not is_valid:
            print_error(error_msg)
            return 1

        # Create the card
        Card.create(deck.id, front, back)
        print()
        print_success("Card added successfully!")
        print(f"📝 Front: {truncate_text(front, 50)}")
        print(f"📝 Back:  {truncate_text(back, 50)}")

        # Show deck stats
        counts = deck.get_card_counts()
        print()
        print_info(f"Deck '{deck.name}' now has {counts['total']} cards")

        return 0

    except Exception as e:
        logger.error(f"Error adding card: {e}")
        print_error("Failed to add card")
        return 1


def cmd_import_cards(args) -> int:
    """Import cards from a CSV/TSV file."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        # Check if file exists
        file_path = Path(args.file)
        if not file_path.exists():
            print_error(f"File '{args.file}' not found")
            return 1

        print_header(f"Import Cards to: {deck.name}")
        print(f"📁 File: {file_path.name}")
        print(f"📊 Delimiter: '{args.delimiter}'")
        print()

        # Parse the file
        try:
            cards_data = parse_csv_file(args.file, args.delimiter)
        except Exception as e:
            print_error(f"Failed to parse file: {e}")
            print_info("Make sure the file has 'front,back' format")
            return 1

        if not cards_data:
            print_warning("No valid cards found in file")
            return 1

        # Show what will be imported
        print_info(f"Found {len(cards_data)} card(s) to import")

        if args.dry_run:
            print_header("Dry Run Preview")
            for i, card_data in enumerate(cards_data[:5]):
                front = truncate_text(card_data['front'], 30)
                back = truncate_text(card_data['back'], 30)
                print(f"  {i+1:2d}. {front:<32} → {back}")
            if len(cards_data) > 5:
                print(f"  ... and {len(cards_data) - 5} more cards")
            print()
            print_info("Use the command without --dry-run to import these cards")
            return 0

        # Confirm import
        if not confirm_action(f"Import {len(cards_data)} cards into '{deck.name}'?", default=True):
            print_info("Import cancelled")
            return 0

        # Import the cards
        print()
        print("Importing cards...")
        imported_count = 0
        failed_cards = []

        for i, card_data in enumerate(cards_data):
            try:
                Card.create(deck.id, card_data['front'], card_data['back'])
                imported_count += 1
                if (i + 1) % 10 == 0:  # Progress indicator
                    print(f"  Imported {i + 1}/{len(cards_data)} cards...")
            except Exception as e:
                logger.warning(f"Failed to import card: {e}")
                failed_cards.append((i + 1, str(e)))

        print()
        print_success(f"Imported {imported_count} cards into '{deck.name}'")

        if failed_cards:
            print_warning(f"{len(failed_cards)} cards failed to import:")
            for line_num, error in failed_cards[:3]:  # Show first 3 errors
                print(f"  Line {line_num}: {error}")
            if len(failed_cards) > 3:
                print(f"  ... and {len(failed_cards) - 3} more errors")

        # Show updated deck stats
        counts = deck.get_card_counts()
        print()
        print_info(f"Deck '{deck.name}' now has {counts['total']} cards")

        return 0

    except Exception as e:
        logger.error(f"Error importing cards: {e}")
        print_error("Failed to import cards")
        return 1


def cmd_import_anki(args) -> int:
    """Import cards from an Anki .apkg file."""
    try:
        # Validate file
        if not os.path.exists(args.file):
            print_error(f"File not found: {args.file}")
            return 1

        if not args.file.endswith('.apkg'):
            print_error("File must be an Anki .apkg file")
            return 1

        # Initialize importer
        importer = AnkiImporter()

        # Validate the .apkg file
        if not importer.validate_file(args.file):
            print_error("Invalid or corrupted .apkg file")
            return 1

        print_header(f"Import Anki Deck: {args.deck}")
        print(f"📁 File: {args.file}")

        if args.dry_run:
            print("🔍 Dry run mode - analyzing file...")

        print()

        # Import the deck
        result = importer.import_deck(
            args.file,
            args.deck,
            dry_run=args.dry_run,
            merge=args.merge
        )

        if result['dry_run']:
            # Show dry run results
            print_info("📊 Import Analysis")
            print(f"Total cards: {result['total_cards']}")
            print(f"New cards: {result['new_cards']}")
            print(f"Learning cards: {result['learning_cards']}")
            print(f"Review cards: {result['review_cards']}")
            print(f"Unique tags: {result['unique_tags']}")

            if result['tags']:
                print(f"Tags: {', '.join(result['tags'][:10])}")
                if len(result['tags']) > 10:
                    print(f"... and {len(result['tags']) - 10} more")

            print(f"Source decks: {', '.join(result['source_decks'])}")
            print(f"Estimated import time: {result['estimated_import_time']:.1f} seconds")

            print()
            print_info("Run without --dry-run to perform the actual import")
        else:
            # Show import results
            print_success(f"Imported {result['imported_cards']} cards into '{result['target_deck']}'")

            if result['skipped_cards'] > 0:
                print_warning(f"Skipped {result['skipped_cards']} cards (empty or invalid)")

            if result['error_cards'] > 0:
                print_warning(f"Failed to import {result['error_cards']} cards")

            print()
            print_info(f"Deck '{result['target_deck']}' is ready for review")

        return 0

    except ValueError as e:
        print_error(str(e))
        return 1
    except Exception as e:
        logger.error(f"Error importing Anki deck: {e}")
        print_error("Failed to import Anki deck")
        return 1


def cmd_import_markdown(args) -> int:
    """Import cards from a Markdown file."""
    try:
        # Validate file
        if not os.path.exists(args.file):
            print_error(f"File not found: {args.file}")
            return 1

        if not any(args.file.endswith(ext) for ext in ['.md', '.markdown']):
            print_error("File must be a Markdown file (.md or .markdown)")
            return 1

        # Initialize importer
        importer = MarkdownImporter()

        # Validate the Markdown file
        if not importer.validate_file(args.file):
            print_error("Invalid Markdown file format")
            return 1

        deck_name = args.deck if args.deck else None

        print_header(f"Import Markdown: {deck_name or 'Auto-detect'}")
        print(f"📁 File: {args.file}")

        if args.dry_run:
            print("🔍 Dry run mode - analyzing file...")

        print()

        # Import the deck
        result = importer.import_deck(
            args.file,
            deck_name,
            dry_run=args.dry_run,
            merge=args.merge
        )

        if result['dry_run']:
            # Show dry run results
            print_info("📊 Import Analysis")
            print(f"Target deck: {result['target_deck']}")
            print(f"Total cards: {result['total_cards']}")
            print(f"Unique tags: {result['unique_tags']}")
            print(f"Average difficulty: {result['average_difficulty']:.2f}")

            if result['tags']:
                print(f"Tags: {', '.join(result['tags'][:10])}")
                if len(result['tags']) > 10:
                    print(f"... and {len(result['tags']) - 10} more")

            if result['deck_info']:
                print(f"Deck info: {result['deck_info']}")

            print(f"Estimated import time: {result['estimated_import_time']:.1f} seconds")

            print()
            print_info("Run without --dry-run to perform the actual import")
        else:
            # Show import results
            print_success(f"Imported {result['imported_cards']} cards into '{result['target_deck']}'")

            if result['skipped_cards'] > 0:
                print_warning(f"Skipped {result['skipped_cards']} cards (empty or invalid)")

            if result['error_cards'] > 0:
                print_warning(f"Failed to import {result['error_cards']} cards")

            print()
            print_info(f"Deck '{result['target_deck']}' is ready for review")

        return 0

    except ValueError as e:
        print_error(str(e))
        return 1
    except Exception as e:
        logger.error(f"Error importing Markdown: {e}")
        print_error("Failed to import Markdown file")
        return 1


def cmd_export_markdown(args) -> int:
    """Export deck to a Markdown file."""
    try:
        # Check if deck exists
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        print_header(f"Export Markdown: {args.deck}")
        print(f"📁 Output: {args.file}")
        print()

        # Initialize exporter
        exporter = MarkdownExporter()

        # Export the deck
        result = exporter.export_deck(
            args.deck,
            args.file,
            include_metadata=not args.no_metadata,
            include_stats=args.include_stats
        )

        # Show export results
        print_success(f"Exported {result['exported_cards']} cards from '{result['deck_name']}'")
        print(f"📄 File size: {result['file_size']:,} bytes")
        print(f"📁 Output file: {result['output_path']}")

        return 0

    except Exception as e:
        logger.error(f"Error exporting Markdown: {e}")
        print_error("Failed to export Markdown file")
        return 1


def cmd_export_csv(args) -> int:
    """Export deck to a CSV file."""
    try:
        # Check if deck exists
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        print_header(f"Export CSV: {args.deck}")
        print(f"📁 Output: {args.file}")
        print(f"🔗 Delimiter: '{args.delimiter}'")
        print()

        # Initialize exporter
        exporter = CSVExporter()

        # Determine export options
        if args.full:
            include_metadata = True
            include_stats = True
            print_info("📊 Full export mode - including all data")
        elif args.scheduling:
            include_metadata = True
            include_stats = True
            print_info("⏰ Scheduling data mode - including scheduling information")
        else:
            include_metadata = not args.no_metadata
            include_stats = False
            if include_metadata:
                print_info("📋 Standard export mode - including metadata")
            else:
                print_info("📄 Basic export mode - front/back only")

        # Export the deck
        result = exporter.export_deck(
            args.deck,
            args.file,
            delimiter=args.delimiter,
            include_metadata=include_metadata,
            include_stats=include_stats
        )

        # Show export results
        print_success(f"Exported {result['exported_cards']} cards from '{result['deck_name']}'")
        print(f"📁 Output file: {result['output_path']}")
        print(f"🔗 Delimiter: '{result['delimiter']}'")

        return 0

    except Exception as e:
        logger.error(f"Error exporting CSV: {e}")
        print_error("Failed to export CSV file")
        return 1


def cmd_search(args) -> int:
    """Search for cards using query string."""
    try:
        print_header("Search Cards")
        print(f"🔍 Query: {args.query}")
        if args.deck:
            print(f"📚 Deck: {args.deck}")
        print()

        # Initialize search engine
        search_engine = SearchEngine()

        # Execute search
        results = search_engine.search(
            args.query,
            deck_name=args.deck,
            limit=args.limit,
            offset=args.offset
        )

        if not results:
            print_info("No cards found matching your search criteria")
            print()
            print("💡 Search tips:")
            print("  • Use quotes for exact phrases: \"hello world\"")
            print("  • Filter by field: tag:spanish difficulty:>0.5")
            print("  • Use operators: word1 AND word2, word1 OR word2")
            print("  • Filter by state: state:learning state:graduated")
            return 0

        # Display results
        print_info(f"Found {len(results)} card(s)")
        if args.offset > 0:
            print_info(f"Showing results {args.offset + 1}-{args.offset + len(results)}")
        print()

        for i, result in enumerate(results, 1):
            card = result.card

            # Card header
            print(f"📄 Card {args.offset + i} (Score: {result.score:.1f})")
            print(f"📚 Deck: {result.deck_name}")

            # Card content
            front_display = card.front[:100] + "..." if len(card.front) > 100 else card.front
            back_display = card.back[:100] + "..." if len(card.back) > 100 else card.back

            print(f"❓ Front: {front_display}")
            print(f"💡 Back: {back_display}")

            # Metadata
            if hasattr(card, 'difficulty'):
                print(f"🎯 Difficulty: {card.difficulty:.2f}")
            if hasattr(card, 'learning_state'):
                print(f"📊 State: {card.learning_state}")
            if card.repetitions > 0:
                print(f"🔄 Repetitions: {card.repetitions}, Interval: {card.interval} days")

            # Show matches if verbose
            if args.verbose and result.matches:
                print("🔍 Matches:")
                for field, matches in result.matches.items():
                    for match in matches[:2]:  # Show first 2 matches per field
                        print(f"  {field}: ...{match}...")

            print()

        # Pagination info
        if len(results) == args.limit:
            next_offset = args.offset + args.limit
            print_info(f"💡 To see more results, use: --offset {next_offset}")

        return 0

    except Exception as e:
        logger.error(f"Error searching cards: {e}")
        print_error("Failed to search cards")
        return 1


def cmd_list_cards(args) -> int:
    """List cards in a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        # Get cards
        if args.due_only:
            cards = Card.get_due_cards(deck.id)
            card_type = "due"
        else:
            cards = Card.get_by_deck(deck.id)
            card_type = "total"

        if not cards:
            if args.due_only:
                print(f"No due cards in deck '{deck.name}'")
            else:
                print(f"No cards in deck '{deck.name}'. Add cards with 'srs add-card \"{deck.name}\"'")
            return 0

        # Apply limit
        display_cards = cards[:args.limit] if args.limit else cards

        print(f"Showing {len(display_cards)} {card_type} card(s) from '{deck.name}':")
        print()

        for i, card in enumerate(display_cards, 1):
            front = truncate_text(card.front, 40)
            back = truncate_text(card.back, 40)

            # Show scheduling info
            if card.repetitions > 0:
                interval_days = card.interval / (24 * 60)
                due_str = card.due_date.strftime('%Y-%m-%d %H:%M')
                print(f"{i:3d}. {front} → {back}")
                print(f"      Reps: {card.repetitions}, Interval: {interval_days:.1f}d, Due: {due_str}")
            else:
                print(f"{i:3d}. {front} → {back} (new)")
            print()

        if len(cards) > len(display_cards):
            print(f"... and {len(cards) - len(display_cards)} more cards")

        return 0

    except Exception as e:
        logger.error(f"Error listing cards: {e}")
        print("Error: Failed to list cards")
        return 1


def cmd_review(args) -> int:
    """Start a review session."""
    try:
        # Set up signal handlers for graceful interruption
        setup_signal_handlers()

        # Check for filtering options
        session = None
        session_type = "Regular"

        # Handle convenience filter options
        if getattr(args, 'hard', False):
            session = create_hard_cards_session(args.deck)
            session_type = "Hard Cards"
        elif getattr(args, 'easy', False):
            session = create_easy_cards_session(args.deck)
            session_type = "Easy Cards"
        elif getattr(args, 'overdue', False):
            session = create_overdue_cards_session(args.deck)
            session_type = "Overdue Cards"
        elif getattr(args, 'learning', False):
            session = create_learning_cards_session(args.deck)
            session_type = "Learning Cards"
        elif getattr(args, 'new_only', False):
            limit = args.limit if args.limit else 20
            session = create_new_cards_session(args.deck, limit)
            session_type = "New Cards"

        # Handle advanced filtering
        elif any([getattr(args, 'difficulty', None), getattr(args, 'interval', None),
                 getattr(args, 'tag', None)]):
            filter_config = ReviewFilter()
            session_type = "Filtered"

            # Parse difficulty filter
            if getattr(args, 'difficulty', None):
                diff_filter = args.difficulty
                if '-' in diff_filter:
                    min_diff, max_diff = map(float, diff_filter.split('-'))
                    filter_config.difficulty_min = min_diff
                    filter_config.difficulty_max = max_diff
                elif diff_filter.startswith('>'):
                    filter_config.difficulty_min = float(diff_filter[1:])
                elif diff_filter.startswith('<'):
                    filter_config.difficulty_max = float(diff_filter[1:])
                else:
                    # Exact match
                    diff_val = float(diff_filter)
                    filter_config.difficulty_min = diff_val - 0.05
                    filter_config.difficulty_max = diff_val + 0.05

            # Parse interval filter
            if getattr(args, 'interval', None):
                int_filter = args.interval
                if '-' in int_filter:
                    min_int, max_int = map(int, int_filter.split('-'))
                    filter_config.interval_min = min_int
                    filter_config.interval_max = max_int
                elif int_filter.startswith('>'):
                    filter_config.interval_min = int(int_filter[1:])
                elif int_filter.startswith('<'):
                    filter_config.interval_max = int(int_filter[1:])
                else:
                    # Exact match
                    int_val = int(int_filter)
                    filter_config.interval_min = int_val
                    filter_config.interval_max = int_val

            # Handle tag filter
            if getattr(args, 'tag', None):
                filter_config.tags = args.tag

            # Handle due/overdue filters
            if getattr(args, 'due_only', False):
                filter_config.due_only = True

            # Create session limits
            limits = SessionLimits(max_cards=args.limit) if args.limit else None
            session = create_filtered_review_session(args.deck, filter_config, limits)

        # Handle time-boxed and smart sessions
        elif hasattr(args, 'time') and args.time:
            # Create session limits
            limits = SessionLimits(
                max_cards=args.limit,
                new_cards_only=getattr(args, 'new_only', False),
                due_cards_only=getattr(args, 'due_only', False)
            )
            session = create_time_boxed_session(args.deck, args.time, limits)
            session_type = f"Time-boxed ({args.time} min)"
        elif hasattr(args, 'smart') and args.smart:
            # Create session limits
            limits = SessionLimits(
                max_cards=args.limit,
                new_cards_only=getattr(args, 'new_only', False),
                due_cards_only=getattr(args, 'due_only', False)
            )
            session = create_smart_review_session(args.deck, limits)
            session_type = "Smart"
        else:
            # Regular session with basic limits
            limits = SessionLimits(
                max_cards=args.limit,
                new_cards_only=getattr(args, 'new_only', False),
                due_cards_only=getattr(args, 'due_only', False)
            )
            session = create_review_session(args.deck)
            if session and (limits.max_cards or limits.new_cards_only or limits.due_cards_only):
                # Apply limits to regular session
                from .scheduling import SmartScheduler
                scheduler = SmartScheduler()
                limited_cards = scheduler._apply_session_limits(session.cards, limits)
                session = create_review_session(args.deck, limited_cards)
            session_type = "Regular"

        if not session:
            print_error(f"Deck '{args.deck}' not found")
            print_info("Use 'srs list-decks' to see available decks")
            return 1

        if not session.has_cards:
            print_header(f"Review: {args.deck}")
            print_info("No cards due for review")
            print("All caught up! 🎉")
            return 0

        # Show session start
        print_header(f"Review Session: {session.deck.name}")
        print(f"📚 Deck: {session.deck.name}")
        print(f"🎯 Session Type: {session_type}")
        print(f"📊 Cards to review: {session.stats.total_cards}")

        # Show session details
        if hasattr(args, 'time') and args.time:
            print(f"⏱️  Time limit: {args.time} minutes")

        # Show filter information
        if getattr(args, 'hard', False):
            print("🔥 Hard cards only (difficulty > 0.7)")
        elif getattr(args, 'easy', False):
            print("😊 Easy cards only (difficulty < 0.3)")
        elif getattr(args, 'overdue', False):
            print("⏰ Overdue cards only")
        elif getattr(args, 'learning', False):
            print("📚 Learning cards only")
        elif getattr(args, 'new_only', False):
            print("🆕 New cards only")
        elif getattr(args, 'due_only', False):
            print("📅 Due cards only")

        if getattr(args, 'difficulty', None):
            print(f"🎯 Difficulty filter: {args.difficulty}")
        if getattr(args, 'interval', None):
            print(f"📊 Interval filter: {args.interval}")
        if getattr(args, 'tag', None):
            print(f"🏷️  Tag filter: {', '.join(args.tag)}")

        if args.limit:
            print(f"🔢 Card limit: {args.limit}")

        print()
        print_info("Press Ctrl+C at any time to stop the session")
        print()
        input("Press ENTER to start reviewing...")

        # Review loop
        try:
            while session.has_cards:
                card = session.current_card
                progress = session.progress

                # Clear screen and show progress
                print("\n" * 2)
                print_separator(70)
                print(f"📚 {session.deck.name} | Card {progress['current_position']} of {progress['total_cards']} | {progress['completion_percentage']:.0f}% complete")
                print_separator(70)
                print()

                # Show question
                print("❓ Question:")
                print(f"   {card.front}")
                print()

                # Wait for user to see answer
                try:
                    input("💡 Press ENTER to reveal answer...")
                except (EOFError, KeyboardInterrupt):
                    raise KeyboardInterrupt

                print()
                print("✅ Answer:")
                print(f"   {card.back}")
                print()
                print_separator(50)

                # Get rating
                while True:
                    print("🎯 How well did you know this?")
                    print()
                    print("  1️⃣  Again     - Completely forgot")
                    print("  2️⃣  Hard      - Struggled to recall")
                    print("  3️⃣  Good      - Recalled with effort")
                    print("  4️⃣  Easy      - Instant recall")
                    print()

                    try:
                        choice = input("Your choice [1-4]: ").strip()
                        if choice in ['1', '2', '3', '4']:
                            rating = int(choice)
                            break
                        elif choice.lower() in ['q', 'quit', 'exit']:
                            raise KeyboardInterrupt
                        else:
                            print_warning("Please enter 1, 2, 3, or 4 (or 'q' to quit)")
                            print()
                    except (ValueError, EOFError, KeyboardInterrupt):
                        raise KeyboardInterrupt

                # Review the card
                session.review_current_card(rating)

                # Show brief feedback
                rating_feedback = {
                    1: "📚 Card will be shown again soon",
                    2: "⏰ Card scheduled for review later",
                    3: "✅ Good! Card scheduled appropriately",
                    4: "🚀 Easy! Card scheduled for later"
                }
                print()
                print(rating_feedback[rating])

                # Brief pause before next card
                if session.has_cards:
                    print()
                    input("Press ENTER for next card...")

        except KeyboardInterrupt:
            print("\n")
            print_warning("Review session interrupted")

            # Ask if user wants to see partial summary
            if session.stats.cards_reviewed > 0:
                if confirm_action("Show session summary?", default=True):
                    show_session_summary(session)
            else:
                print_info("No cards were reviewed")
            return 0

        # Show session summary
        print("\n")
        print_header("🎉 Review Session Complete!")
        show_session_summary(session)

        return 0

    except Exception as e:
        logger.error(f"Error in review session: {e}")
        print_error("Review session failed")
        return 1


def show_session_summary(session):
    """Display a formatted session summary."""
    summary = session.get_session_summary()

    print(f"📊 Cards reviewed: {summary['cards_reviewed']}")
    print(f"⏱️  Duration: {format_duration(summary['duration_minutes'] * 60)}")
    print(f"📈 Average rating: {summary['average_rating']:.1f}")
    print()

    # Rating breakdown with visual indicators
    print("📋 Rating breakdown:")
    rating_info = {
        1: ("Again", "🔴"),
        2: ("Hard", "🟡"),
        3: ("Good", "🟢"),
        4: ("Easy", "🔵")
    }

    for rating, count in summary['rating_counts'].items():
        if count > 0:
            name, emoji = rating_info[rating]
            percentage = (count / summary['cards_reviewed']) * 100
            print(f"  {emoji} {name}: {count} ({percentage:.0f}%)")

    # Encouragement message
    if summary['average_rating'] >= 3.5:
        print("\n🌟 Excellent work! You're mastering these cards!")
    elif summary['average_rating'] >= 2.5:
        print("\n👍 Good progress! Keep up the practice!")
    else:
        print("\n💪 Keep practicing! You'll improve with time!")


def cmd_status(args) -> int:
    """Show system status."""
    try:
        decks = Deck.get_all()

        if not decks:
            print_header("📊 SRS Status")
            print_info("No decks found")
            print("Get started by creating your first deck:")
            print("  srs create-deck \"My First Deck\"")
            return 0

        print_header("📊 SRS Status")

        total_due = 0
        total_new = 0
        total_cards = 0
        decks_with_due = []

        # Collect deck information
        for deck in decks:
            counts = deck.get_card_counts()
            total_due += counts['due']
            total_new += counts['new']
            total_cards += counts['total']

            if counts['due'] > 0:
                decks_with_due.append((deck, counts))

        # Show overview
        print(f"📚 Total decks: {len(decks)}")
        print(f"📊 Total cards: {total_cards}")
        print(f"🔥 Cards due: {total_due}")
        print(f"🆕 New cards: {total_new}")
        print()

        # Show decks with due cards first
        if decks_with_due:
            print_separator()
            print("🔥 Decks with cards due for review:")
            print()

            for deck, counts in decks_with_due:
                status_icon = "🔥"
                print(f"  {status_icon} {deck.name:<25} {counts['due']} due, {counts['new']} new")

                if args.detailed:
                    due_cards = Card.get_due_cards(deck.id)
                    if due_cards:
                        oldest_due = min(card.due_date for card in due_cards)
                        print(f"     Oldest due: {oldest_due.strftime('%Y-%m-%d %H:%M')}")
            print()

        # Show all decks if detailed or if no due cards
        if args.detailed or not decks_with_due:
            print_separator()
            print("📚 All decks:")
            print()

            for i, deck in enumerate(decks, 1):
                counts = deck.get_card_counts()
                count_str = format_card_counts(counts)
                status_icon = "🔥" if counts['due'] > 0 else "📚"

                print(f"{i:2d}. {status_icon} {deck.name:<25} {count_str}")

                if args.detailed:
                    print(f"     Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
                    if counts['total'] > 0:
                        print(f"     Total: {counts['total']}, Due: {counts['due']}, New: {counts['new']}")
                    print()

        # Show recommendations
        print_separator()
        if total_due > 0:
            print_info(f"You have {total_due} cards ready for review!")
            print("Start reviewing with:")
            for deck, counts in decks_with_due[:3]:  # Show top 3
                print(f"  srs review \"{deck.name}\"")
            if len(decks_with_due) > 3:
                print(f"  ... and {len(decks_with_due) - 3} more decks")
        elif total_new > 0:
            print_info(f"You have {total_new} new cards to learn!")
            print("Start learning with:")
            print("  srs review \"<deck-name>\"")
        else:
            print_success("All caught up! 🎉")
            print("Add more cards or wait for scheduled reviews.")

        return 0

    except Exception as e:
        logger.error(f"Error showing status: {e}")
        print_error("Failed to show status")
        return 1


def cmd_stats(args) -> int:
    """Show detailed statistics and analytics."""
    try:
        from datetime import date, timedelta
        import json

        stats_engine = StatisticsEngine()
        progress_tracker = ProgressTracker()

        # Determine time period
        if args.period == 'week':
            start_date = date.today() - timedelta(days=7)
            period_name = "Last 7 days"
        elif args.period == 'month':
            start_date = date.today() - timedelta(days=30)
            period_name = "Last 30 days"
        elif args.period == 'year':
            start_date = date.today() - timedelta(days=365)
            period_name = "Last year"
        else:  # all
            start_date = date.today() - timedelta(days=3650)  # ~10 years
            period_name = "All time"

        end_date = date.today()

        print_header(f"📊 Statistics - {period_name}")

        # Get deck-specific stats if requested
        target_deck = None
        if args.deck:
            target_deck = Deck.get_by_name(args.deck)
            if not target_deck:
                print_error(f"Deck '{args.deck}' not found")
                return 1
            print_info(f"Showing statistics for deck: {target_deck.name}")
            print()

        # Show different views based on flags
        if args.retention:
            return _show_retention_analysis(stats_engine, start_date, end_date, target_deck)
        elif args.forecast:
            return _show_review_forecast(target_deck)
        elif args.performance:
            return _show_performance_trends(stats_engine, start_date, end_date, target_deck)
        elif args.export:
            return _export_statistics(stats_engine, progress_tracker, start_date, end_date,
                                    target_deck, args.export, args.output)
        else:
            return _show_general_statistics(stats_engine, progress_tracker, start_date,
                                          end_date, target_deck, period_name)

    except Exception as e:
        logger.error(f"Error showing statistics: {e}")
        print_error("Failed to show statistics")
        return 1


def _show_general_statistics(stats_engine, progress_tracker, start_date, end_date,
                           target_deck, period_name) -> int:
    """Show general statistics overview."""
    # Get daily statistics
    daily_stats = stats_engine.get_daily_stats(start_date, end_date)

    # Calculate summary metrics
    total_cards_reviewed = sum(s.cards_reviewed for s in daily_stats)
    total_new_cards = sum(s.new_cards_learned for s in daily_stats)
    total_study_time = sum(s.review_time_seconds for s in daily_stats)

    # Get retention rate
    retention_rate = stats_engine.calculate_retention_rate(start_date, end_date,
                                                         target_deck.id if target_deck else None)

    # Get progress summary
    progress_summary = progress_tracker.get_progress_summary()

    # Show summary box
    summary_stats = {
        'Cards Reviewed': total_cards_reviewed,
        'New Cards Learned': total_new_cards,
        'Study Time': format_duration(total_study_time),
        'Retention Rate': f"{retention_rate:.1f}%",
        'Current Streak': f"{progress_summary['daily_streak']} days",
        'Achievements': f"{progress_summary['achievements_unlocked']}/{progress_summary['total_achievements']}"
    }

    print(create_summary_box(f"Summary - {period_name}", summary_stats))
    print()

    # Show daily activity chart if we have data
    if daily_stats:
        print_info("📈 Daily Activity")
        print_separator()

        # Create activity chart data (last 14 days for readability)
        recent_stats = daily_stats[-14:] if len(daily_stats) > 14 else daily_stats
        chart_data = [(s.date.strftime('%m/%d'), s.cards_reviewed) for s in recent_stats]

        if chart_data:
            chart = create_line_chart(chart_data, height=8, width=50,
                                    title="Cards Reviewed per Day")
            print(chart)
            print()

    # Show rating distribution
    if target_deck:
        deck_id = target_deck.id
    else:
        deck_id = None

    rating_query = """
    SELECT rating, COUNT(*) as count
    FROM reviews r
    JOIN cards c ON r.card_id = c.id
    WHERE r.reviewed_at BETWEEN ? AND ?
    """
    params = [start_date.isoformat(), end_date.isoformat()]

    if deck_id:
        rating_query += " AND c.deck_id = ?"
        params.append(deck_id)

    rating_query += " GROUP BY rating ORDER BY rating"

    rating_results = stats_engine.db.execute_query(rating_query, params)

    if rating_results:
        print_info("⭐ Rating Distribution")
        print_separator()

        rating_data = {f"Rating {row['rating']}": row['count'] for row in rating_results}
        chart = create_horizontal_bar_chart(rating_data, max_width=30,
                                          title="Review Ratings")
        print(chart)
        print()

    # Show achievements
    achievements = progress_tracker.get_user_achievements()
    if achievements:
        print_info("🏆 Recent Achievements")
        print_separator()
        achievement_display = create_achievement_display(achievements)
        print(achievement_display)

    return 0


def _show_retention_analysis(stats_engine, start_date, end_date, target_deck) -> int:
    """Show detailed retention rate analysis."""
    from datetime import date, timedelta

    print_info("🎯 Retention Rate Analysis")
    print_separator()

    deck_id = target_deck.id if target_deck else None

    # Calculate retention rates for different periods
    periods = [
        ('Last 7 days', 7),
        ('Last 30 days', 30),
        ('Last 90 days', 90),
        ('All time', 365)
    ]

    retention_data = {}
    for period_name, days in periods:
        period_start = date.today() - timedelta(days=days)
        retention = stats_engine.calculate_retention_rate(period_start, end_date, deck_id)
        retention_data[period_name] = f"{retention:.1f}%"

    print(create_summary_box("Retention Rates by Period", retention_data))
    print()

    # Show retention by rating
    rating_retention_query = """
    SELECT
        rating,
        COUNT(*) as total_reviews,
        SUM(CASE WHEN rating >= 3 THEN 1 ELSE 0 END) as successful_reviews
    FROM reviews r
    JOIN cards c ON r.card_id = c.id
    WHERE r.reviewed_at BETWEEN ? AND ?
    """
    params = [start_date.isoformat(), end_date.isoformat()]

    if deck_id:
        rating_retention_query += " AND c.deck_id = ?"
        params.append(deck_id)

    rating_retention_query += " GROUP BY rating ORDER BY rating"

    results = stats_engine.db.execute_query(rating_retention_query, params)

    if results:
        print_info("Retention by Initial Rating")
        print_separator()

        for row in results:
            rating = row['rating']
            total = row['total_reviews']
            successful = row['successful_reviews']
            retention = (successful / total * 100) if total > 0 else 0

            bar = create_progress_bar(successful, total, width=20)
            print(f"Rating {rating}: {bar}")
        print()

    return 0


def _show_review_forecast(target_deck) -> int:
    """Show upcoming review forecast."""
    from datetime import date, timedelta

    print_info("📅 Review Forecast")
    print_separator()

    # Get cards due in the next 7 days
    forecast_data = {}

    for i in range(7):
        forecast_date = date.today() + timedelta(days=i)

        # Query for cards due on this date
        query = """
        SELECT COUNT(*) as count
        FROM cards c
        WHERE DATE(c.due_date) = ?
        """
        params = [forecast_date.isoformat()]

        if target_deck:
            query += " AND c.deck_id = ?"
            params.append(target_deck.id)

        from .database import get_database
        db = get_database()
        results = db.execute_query(query, params)

        count = results[0]['count'] if results else 0
        day_name = forecast_date.strftime('%a %m/%d')
        forecast_data[day_name] = count

    if any(count > 0 for count in forecast_data.values()):
        chart = create_horizontal_bar_chart(forecast_data, max_width=30,
                                          title="Cards Due by Day")
        print(chart)
    else:
        print("No cards due in the next 7 days.")

    print()
    return 0


def _show_performance_trends(stats_engine, start_date, end_date, target_deck) -> int:
    """Show performance trends analysis."""
    from datetime import date, timedelta

    print_info("📈 Performance Trends")
    print_separator()

    deck_id = target_deck.id if target_deck else None

    # Get weekly performance data
    weekly_data = []
    current_date = start_date

    while current_date <= end_date:
        week_end = min(current_date + timedelta(days=6), end_date)

        # Get stats for this week
        weekly_stats = stats_engine.get_daily_stats(current_date, week_end)
        week_cards = sum(s.cards_reviewed for s in weekly_stats)
        week_retention = stats_engine.calculate_retention_rate(current_date, week_end, deck_id)

        week_label = current_date.strftime('%m/%d')
        weekly_data.append((week_label, week_cards))

        current_date = week_end + timedelta(days=1)

    if weekly_data:
        chart = create_line_chart(weekly_data, height=8, width=50,
                                title="Weekly Review Activity")
        print(chart)
        print()

    # Show average performance metrics
    all_daily_stats = stats_engine.get_daily_stats(start_date, end_date)
    if all_daily_stats:
        active_days = [s for s in all_daily_stats if s.cards_reviewed > 0]

        if active_days:
            avg_cards_per_day = sum(s.cards_reviewed for s in active_days) / len(active_days)
            avg_study_time = sum(s.review_time_seconds for s in active_days) / len(active_days)
            avg_retention = sum(s.retention_rate for s in active_days) / len(active_days)

            performance_stats = {
                'Active Days': len(active_days),
                'Avg Cards/Day': f"{avg_cards_per_day:.1f}",
                'Avg Study Time': format_duration(int(avg_study_time)),
                'Avg Retention': f"{avg_retention:.1f}%"
            }

            print(create_summary_box("Performance Averages", performance_stats))

    return 0


def _export_statistics(stats_engine, progress_tracker, start_date, end_date,
                      target_deck, export_format, output_path) -> int:
    """Export statistics to file."""
    import json
    from datetime import datetime

    try:
        # Collect all statistics data
        daily_stats = stats_engine.get_daily_stats(start_date, end_date)
        progress_summary = progress_tracker.get_progress_summary()
        achievements = progress_tracker.get_user_achievements()

        deck_id = target_deck.id if target_deck else None
        retention_rate = stats_engine.calculate_retention_rate(start_date, end_date, deck_id)

        # Prepare export data
        export_data = {
            'generated_at': datetime.now().isoformat(),
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'deck': target_deck.name if target_deck else 'All Decks',
            'summary': {
                'total_cards_reviewed': sum(s.cards_reviewed for s in daily_stats),
                'total_new_cards': sum(s.new_cards_learned for s in daily_stats),
                'total_study_time_seconds': sum(s.review_time_seconds for s in daily_stats),
                'retention_rate': retention_rate,
                'daily_streak': progress_summary['daily_streak'],
                'achievements_unlocked': progress_summary['achievements_unlocked']
            },
            'daily_statistics': [
                {
                    'date': s.date.isoformat(),
                    'cards_reviewed': s.cards_reviewed,
                    'new_cards_learned': s.new_cards_learned,
                    'review_time_seconds': s.review_time_seconds,
                    'retention_rate': s.retention_rate
                }
                for s in daily_stats
            ],
            'achievements': [
                {
                    'name': a['name'],
                    'description': a['description'],
                    'icon': a['icon'],
                    'unlocked_at': a['unlocked_at'].isoformat() if isinstance(a['unlocked_at'], datetime) else str(a['unlocked_at'])
                }
                for a in achievements
            ]
        }

        # Determine output path
        if not output_path:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            deck_suffix = f"_{target_deck.name}" if target_deck else "_all_decks"
            output_path = f"srs_stats{deck_suffix}_{timestamp}.{export_format}"

        # Export based on format
        if export_format == 'json':
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
        elif export_format == 'csv':
            # Export daily statistics as CSV
            csv_data = export_data['daily_statistics']
            if not csv_data:
                # Create a minimal CSV with summary data if no daily stats
                csv_data = [export_data['summary']]

            if not export_data_to_csv(csv_data, output_path):
                print_error("Failed to export CSV data")
                return 1

        print_success(f"Statistics exported to: {output_path}")
        return 0

    except Exception as e:
        logger.error(f"Export failed: {e}")
        print_error("Failed to export statistics")
        return 1


def cli(argv=None):
    """Main CLI entry point."""
    # Set up signal handlers for graceful shutdown
    setup_signal_handlers()

    parser = setup_argument_parser()
    args = parser.parse_args(argv)

    # Initialize the application
    try:
        initialize_srs(args.config if hasattr(args, 'config') else None)

        # Set log level based on verbose flag
        if hasattr(args, 'verbose') and args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

    except Exception as e:
        print_error(f"Failed to initialize SRS: {e}")
        return 1

    # Handle no command
    if not args.command:
        print_header("🧠 Spaced Repetition System")
        print("A terminal-based flashcard application for efficient learning.")
        print()
        parser.print_help()
        print()
        print_info("Start by creating your first deck:")
        print("  srs create-deck \"My First Deck\"")
        return 0

    # Dispatch to command handlers
    command_handlers = {
        'create-deck': cmd_create_deck,
        'list-decks': cmd_list_decks,
        'delete-deck': cmd_delete_deck,
        'add-card': cmd_add_card,
        'import-cards': cmd_import_cards,
        'import-anki': cmd_import_anki,
        'import-markdown': cmd_import_markdown,
        'export-markdown': cmd_export_markdown,
        'export-csv': cmd_export_csv,
        'search': cmd_search,
        'list-cards': cmd_list_cards,
        'review': cmd_review,
        'status': cmd_status,
        'stats': cmd_stats,
    }

    handler = command_handlers.get(args.command)
    if handler:
        try:
            return handler(args)
        except KeyboardInterrupt:
            print("\n")
            print_info("Operation cancelled by user")
            return 1
        except Exception as e:
            logger.error(f"Unexpected error in command {args.command}: {e}")
            print_error(f"Command '{args.command}' failed")
            return 1
    else:
        print_error(f"Unknown command '{args.command}'")
        print_info("Use 'srs --help' to see available commands")
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(cli())
