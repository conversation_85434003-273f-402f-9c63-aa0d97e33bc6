"""
Review session management for the spaced repetition system.

This module handles review session logic, card ordering, progress tracking,
and session state management.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from uuid import uuid4

from .models import Card, Review, Deck
from .algorithm import get_algorithm
from .statistics import StatisticsEngine
from .progress import ProgressTracker
from .scheduling import SmartScheduler, SessionLimits
from .search import SearchEngine

logger = logging.getLogger(__name__)


@dataclass
class SessionStats:
    """Statistics for a review session."""
    total_cards: int = 0
    cards_reviewed: int = 0
    cards_remaining: int = 0
    rating_counts: Dict[int, int] = field(default_factory=lambda: {1: 0, 2: 0, 3: 0, 4: 0})
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def duration_minutes(self) -> float:
        """Get session duration in minutes."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds() / 60
        return 0.0
    
    @property
    def completion_percentage(self) -> float:
        """Get completion percentage."""
        if self.total_cards == 0:
            return 100.0
        return (self.cards_reviewed / self.total_cards) * 100


class ReviewSession:
    """
    Manages a review session for a deck or set of cards.
    
    Handles card ordering, progress tracking, session interruption/resumption,
    and statistics collection.
    """
    
    def __init__(self, deck: Deck, cards: Optional[List[Card]] = None):
        """
        Initialize a review session.
        
        Args:
            deck: The deck being reviewed
            cards: Optional list of specific cards to review
        """
        self.deck = deck
        self.algorithm = get_algorithm()
        self.stats = SessionStats()
        self.session_id = str(uuid4())  # Generate unique session ID

        # Get cards to review (respecting daily limits)
        if cards is not None:
            self.cards = cards.copy()
        else:
            # Use deck's limited cards that respect daily limits
            self.cards = deck.get_limited_due_cards(include_new=True)

        # Order cards by priority (due cards first, then by due date)
        self.cards = self._order_cards(self.cards)

        self.current_index = 0
        self.stats.total_cards = len(self.cards)
        self.stats.cards_remaining = len(self.cards)
        self.stats.start_time = datetime.now()
        self._card_start_time = None  # Track when current card was shown
        
        logger.info(f"Started review session for deck '{deck.name}' with {len(self.cards)} cards")
    
    def _order_cards(self, cards: List[Card]) -> List[Card]:
        """
        Order cards by review priority.
        
        Priority order:
        1. Due cards (due_date <= now) ordered by due_date (oldest first)
        2. New cards (repetitions = 0) ordered by creation date
        3. Future cards ordered by due_date
        """
        now = datetime.now()
        
        # Separate cards by type
        due_cards = [c for c in cards if c.due_date <= now and c.repetitions > 0]
        new_cards = [c for c in cards if c.repetitions == 0]
        future_cards = [c for c in cards if c.due_date > now and c.repetitions > 0]
        
        # Sort each category
        due_cards.sort(key=lambda c: c.due_date)
        new_cards.sort(key=lambda c: c.created_at or datetime.now())
        future_cards.sort(key=lambda c: c.due_date)
        
        # Combine in priority order
        ordered_cards = due_cards + new_cards + future_cards
        
        logger.debug(f"Ordered cards: {len(due_cards)} due, {len(new_cards)} new, {len(future_cards)} future")
        return ordered_cards
    
    @property
    def has_cards(self) -> bool:
        """Check if there are cards remaining in the session."""
        return self.current_index < len(self.cards)
    
    @property
    def current_card(self) -> Optional[Card]:
        """Get the current card to review."""
        if self.has_cards:
            # Start timing when card is first accessed
            if self._card_start_time is None:
                self._card_start_time = datetime.now()
            return self.cards[self.current_index]
        return None
    
    @property
    def progress(self) -> Dict[str, Any]:
        """Get current session progress information."""
        return {
            'current_position': self.current_index + 1 if self.has_cards else self.stats.total_cards,
            'total_cards': self.stats.total_cards,
            'cards_reviewed': self.stats.cards_reviewed,
            'cards_remaining': self.stats.cards_remaining,
            'completion_percentage': self.stats.completion_percentage,
            'deck_name': self.deck.name
        }
    
    def review_current_card(self, rating: int) -> bool:
        """
        Review the current card with the given rating.
        
        Args:
            rating: User rating (1-4)
            
        Returns:
            True if review was successful, False if no current card
        """
        if not self.has_cards:
            logger.warning("Attempted to review card but no cards remaining")
            return False
        
        card = self.current_card
        if not card:
            return False
        
        try:
            # Calculate response time if we have a start time
            response_time = 0
            if self._card_start_time:
                response_time = int((datetime.now() - self._card_start_time).total_seconds() * 1000)

            # Apply SM-2 algorithm to get new scheduling
            result = self.algorithm.review_card(card, rating)

            # Update card with new scheduling parameters
            card.interval = result.interval
            card.repetitions = result.repetitions
            card.ease_factor = result.ease_factor
            card.due_date = result.due_date

            # Update difficulty tracking (Phase 1.1.2)
            card.update_difficulty(rating, review_duration=response_time)

            # Save card changes to database
            card.save()

            # Create review record with statistics
            Review.create(
                card.id,
                rating,
                response_time=response_time,
                session_id=self.session_id,
                review_type='normal'
            )

            # Update session statistics
            self.stats.cards_reviewed += 1
            self.stats.cards_remaining -= 1
            self.stats.rating_counts[rating] += 1

            # Move to next card
            self.current_index += 1
            self._card_start_time = datetime.now() if self.has_cards else None
            
            logger.info(f"Reviewed card {card.id} with rating {rating}, next due: {result.due_date}")
            return True
            
        except Exception as e:
            logger.error(f"Error reviewing card {card.id}: {e}")
            return False
    
    def skip_current_card(self) -> bool:
        """
        Skip the current card without reviewing it.
        
        Returns:
            True if skip was successful, False if no current card
        """
        if not self.has_cards:
            return False
        
        logger.info(f"Skipped card {self.current_card.id}")
        self.current_index += 1
        self.stats.cards_remaining -= 1
        return True
    
    def end_session(self):
        """End the review session and finalize statistics."""
        self.stats.end_time = datetime.now()

        # Update statistics when session ends
        self._update_statistics()

        logger.info(f"Ended review session for deck '{self.deck.name}'")
        logger.info(f"Session stats: {self.stats.cards_reviewed}/{self.stats.total_cards} cards reviewed")
        logger.info(f"Duration: {self.stats.duration_minutes:.1f} minutes")
        logger.info(f"Ratings: {self.stats.rating_counts}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the completed session.
        
        Returns:
            Dictionary with session statistics and summary
        """
        if not self.stats.end_time:
            self.end_session()
        
        return {
            'deck_name': self.deck.name,
            'total_cards': self.stats.total_cards,
            'cards_reviewed': self.stats.cards_reviewed,
            'completion_percentage': self.stats.completion_percentage,
            'duration_minutes': self.stats.duration_minutes,
            'rating_counts': self.stats.rating_counts.copy(),
            'average_rating': self._calculate_average_rating(),
            'start_time': self.stats.start_time,
            'end_time': self.stats.end_time
        }
    
    def _calculate_average_rating(self) -> float:
        """Calculate the average rating for the session."""
        total_ratings = sum(self.stats.rating_counts.values())
        if total_ratings == 0:
            return 0.0
        
        weighted_sum = sum(rating * count for rating, count in self.stats.rating_counts.items())
        return weighted_sum / total_ratings
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current session state for resumption.
        
        Returns:
            Dictionary with session state data
        """
        return {
            'deck_id': self.deck.id,
            'current_index': self.current_index,
            'card_ids': [card.id for card in self.cards],
            'stats': {
                'total_cards': self.stats.total_cards,
                'cards_reviewed': self.stats.cards_reviewed,
                'cards_remaining': self.stats.cards_remaining,
                'rating_counts': self.stats.rating_counts.copy(),
                'start_time': self.stats.start_time.isoformat() if self.stats.start_time else None
            }
        }
    
    @classmethod
    def restore_from_state(cls, state: Dict[str, Any]) -> 'ReviewSession':
        """
        Restore a review session from saved state.
        
        Args:
            state: Previously saved session state
            
        Returns:
            Restored ReviewSession instance
        """
        # Get deck
        deck = Deck.get_by_id(state['deck_id'])
        if not deck:
            raise ValueError(f"Deck {state['deck_id']} not found")
        
        # Get cards by IDs
        cards = []
        for card_id in state['card_ids']:
            card = Card.get_by_id(card_id)
            if card:
                cards.append(card)
        
        # Create session
        session = cls(deck, cards)
        
        # Restore state
        session.current_index = state['current_index']
        session.stats.total_cards = state['stats']['total_cards']
        session.stats.cards_reviewed = state['stats']['cards_reviewed']
        session.stats.cards_remaining = state['stats']['cards_remaining']
        session.stats.rating_counts = state['stats']['rating_counts']
        
        if state['stats']['start_time']:
            session.stats.start_time = datetime.fromisoformat(state['stats']['start_time'])
        
        logger.info(f"Restored review session for deck '{deck.name}' at position {session.current_index}")
        return session

    def _update_statistics(self):
        """Update daily and deck statistics after session completion."""
        if self.stats.cards_reviewed == 0:
            return  # No reviews to record

        try:
            stats_engine = StatisticsEngine()
            progress_tracker = ProgressTracker()

            # Update daily statistics
            stats_engine.update_daily_statistics()

            # Update deck-specific statistics
            stats_engine.update_deck_statistics(self.deck.id)

            # Update progress tracking
            if self.stats.cards_reviewed > 0:
                # Update daily review streak
                progress_tracker.update_streak('daily_review')

                # Check for achievements
                progress_tracker.check_review_achievements()
                progress_tracker.check_session_achievements(self.get_session_summary())

            logger.debug(f"Updated statistics and progress for session {self.session_id}")
        except Exception as e:
            logger.error(f"Failed to update statistics and progress: {e}")
            # Don't fail the session if statistics update fails


def create_review_session(deck_name: str, cards: Optional[List[Card]] = None) -> Optional[ReviewSession]:
    """
    Create a new review session for a deck.
    
    Args:
        deck_name: Name of the deck to review
        cards: Optional specific cards to review
        
    Returns:
        ReviewSession instance or None if deck not found
    """
    deck = Deck.get_by_name(deck_name)
    if not deck:
        logger.error(f"Deck '{deck_name}' not found")
        return None
    
    return ReviewSession(deck, cards)


def create_smart_review_session(deck_name: str, limits: Optional[SessionLimits] = None) -> Optional[ReviewSession]:
    """
    Create a smart review session with intelligent card selection.

    Args:
        deck_name: Name of the deck to review
        limits: Session limits configuration

    Returns:
        ReviewSession instance or None if deck not found
    """
    deck = Deck.get_by_name(deck_name)
    if not deck:
        logger.error(f"Deck '{deck_name}' not found")
        return None

    if limits is None:
        # Use deck's default limits
        limits = SessionLimits(
            max_new_cards=deck.daily_new_limit,
            max_review_cards=deck.daily_review_limit
        )

    # Create smart scheduler and get cards
    scheduler = SmartScheduler()
    cards = scheduler.create_smart_session(deck, limits)

    if not cards:
        logger.info(f"No cards available for review in deck '{deck_name}'")
        return None

    # Get session statistics
    stats = scheduler.get_session_statistics(cards)
    logger.info(f"Smart session created: {stats['total_cards']} cards, "
               f"~{stats['estimated_time_minutes']} minutes")

    return ReviewSession(deck, cards)


def create_time_boxed_session(deck_name: str, max_time_minutes: int,
                            limits: Optional[SessionLimits] = None) -> Optional[ReviewSession]:
    """
    Create a time-boxed review session.

    Args:
        deck_name: Name of the deck to review
        max_time_minutes: Maximum session time in minutes
        limits: Additional session limits

    Returns:
        ReviewSession instance or None if deck not found
    """
    deck = Deck.get_by_name(deck_name)
    if not deck:
        logger.error(f"Deck '{deck_name}' not found")
        return None

    # Create smart scheduler and get time-boxed cards
    scheduler = SmartScheduler()
    cards = scheduler.create_time_boxed_session(deck, max_time_minutes, limits)

    if not cards:
        logger.info(f"No cards available for time-boxed session in deck '{deck_name}'")
        return None

    # Get session statistics
    stats = scheduler.get_session_statistics(cards)
    logger.info(f"Time-boxed session created: {stats['total_cards']} cards, "
               f"~{stats['estimated_time_minutes']} minutes")

    return ReviewSession(deck, cards)


@dataclass
class ReviewFilter:
    """Filter configuration for review sessions."""
    difficulty_min: Optional[float] = None
    difficulty_max: Optional[float] = None
    interval_min: Optional[int] = None
    interval_max: Optional[int] = None
    repetitions_min: Optional[int] = None
    repetitions_max: Optional[int] = None
    learning_states: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    card_types: Optional[List[str]] = None  # 'new', 'learning', 'review', 'overdue'
    due_only: bool = False
    overdue_only: bool = False

    def to_search_query(self) -> str:
        """Convert filter to search query string."""
        query_parts = []

        if self.difficulty_min is not None:
            query_parts.append(f"difficulty:>={self.difficulty_min}")
        if self.difficulty_max is not None:
            query_parts.append(f"difficulty:<={self.difficulty_max}")
        if self.interval_min is not None:
            query_parts.append(f"interval:>={self.interval_min}")
        if self.interval_max is not None:
            query_parts.append(f"interval:<={self.interval_max}")
        if self.repetitions_min is not None:
            query_parts.append(f"repetitions:>={self.repetitions_min}")
        if self.repetitions_max is not None:
            query_parts.append(f"repetitions:<={self.repetitions_max}")

        if self.learning_states:
            state_queries = [f"state:{state}" for state in self.learning_states]
            query_parts.append(f"({' OR '.join(state_queries)})")

        if self.tags:
            tag_queries = [f"tag:{tag}" for tag in self.tags]
            query_parts.append(f"({' OR '.join(tag_queries)})")

        return " AND ".join(query_parts) if query_parts else ""


def create_filtered_review_session(deck_name: str, filter_config: ReviewFilter,
                                  limits: Optional[SessionLimits] = None) -> Optional[ReviewSession]:
    """
    Create a review session with advanced filtering.

    Args:
        deck_name: Name of the deck to review
        filter_config: Filter configuration
        limits: Session limits configuration

    Returns:
        ReviewSession instance or None if deck not found
    """
    deck = Deck.get_by_name(deck_name)
    if not deck:
        logger.error(f"Deck '{deck_name}' not found")
        return None

    # Get all cards from deck
    all_cards = Card.get_by_deck(deck.id)

    # Apply filters
    filtered_cards = _apply_review_filters(all_cards, filter_config)

    if not filtered_cards:
        logger.info(f"No cards match the filter criteria in deck '{deck_name}'")
        return None

    # Apply session limits if specified
    if limits:
        filtered_cards = _apply_session_limits(filtered_cards, limits)

    return ReviewSession(deck, filtered_cards)


def _apply_review_filters(cards: List[Card], filter_config: ReviewFilter) -> List[Card]:
    """Apply review filters to a list of cards."""
    filtered_cards = []
    now = datetime.now()

    for card in cards:
        # Check difficulty filters
        if filter_config.difficulty_min is not None and card.difficulty < filter_config.difficulty_min:
            continue
        if filter_config.difficulty_max is not None and card.difficulty > filter_config.difficulty_max:
            continue

        # Check interval filters
        if filter_config.interval_min is not None and card.interval < filter_config.interval_min:
            continue
        if filter_config.interval_max is not None and card.interval > filter_config.interval_max:
            continue

        # Check repetitions filters
        if filter_config.repetitions_min is not None and card.repetitions < filter_config.repetitions_min:
            continue
        if filter_config.repetitions_max is not None and card.repetitions > filter_config.repetitions_max:
            continue

        # Check learning state filters
        if filter_config.learning_states:
            card_state = getattr(card, 'learning_state', 'new')
            if card_state not in filter_config.learning_states:
                continue

        # Check card type filters
        if filter_config.card_types:
            card_type = _get_card_type(card, now)
            if card_type not in filter_config.card_types:
                continue

        # Check due/overdue filters
        if filter_config.due_only and not _is_card_due(card, now):
            continue
        if filter_config.overdue_only and not _is_card_overdue(card, now):
            continue

        # TODO: Check tag filters when tag system is implemented

        filtered_cards.append(card)

    return filtered_cards


def _apply_session_limits(cards: List[Card], limits: SessionLimits) -> List[Card]:
    """Apply session limits to filtered cards."""
    now = datetime.now()

    # Separate cards by type
    new_cards = [card for card in cards if card.repetitions == 0]
    review_cards = [card for card in cards if card.repetitions > 0 and _is_card_due(card, now)]

    # Apply limits
    limited_new = new_cards[:limits.max_new_cards] if limits.max_new_cards else new_cards
    limited_review = review_cards[:limits.max_review_cards] if limits.max_review_cards else review_cards

    return limited_new + limited_review


def _get_card_type(card: Card, now: datetime) -> str:
    """Get the type of card for filtering purposes."""
    if card.repetitions == 0:
        return 'new'
    elif hasattr(card, 'learning_state') and card.learning_state in ['learning', 'relearning']:
        return 'learning'
    elif _is_card_overdue(card, now):
        return 'overdue'
    else:
        return 'review'


def _is_card_due(card: Card, now: datetime) -> bool:
    """Check if a card is due for review."""
    if not card.due_date:
        return card.repetitions == 0  # New cards are always due

    if isinstance(card.due_date, str):
        from datetime import datetime
        due_date = datetime.fromisoformat(card.due_date.replace('Z', '+00:00'))
    else:
        due_date = card.due_date

    return due_date <= now


def _is_card_overdue(card: Card, now: datetime) -> bool:
    """Check if a card is overdue for review."""
    if not card.due_date or card.repetitions == 0:
        return False

    if isinstance(card.due_date, str):
        from datetime import datetime
        due_date = datetime.fromisoformat(card.due_date.replace('Z', '+00:00'))
    else:
        due_date = card.due_date

    # Consider overdue if more than 1 day past due date
    from datetime import timedelta
    return due_date < (now - timedelta(days=1))


# Convenience functions for common filter types
def create_hard_cards_session(deck_name: str, difficulty_threshold: float = 0.7) -> Optional[ReviewSession]:
    """Create a session for reviewing hard cards only."""
    filter_config = ReviewFilter(difficulty_min=difficulty_threshold)
    return create_filtered_review_session(deck_name, filter_config)


def create_easy_cards_session(deck_name: str, difficulty_threshold: float = 0.3) -> Optional[ReviewSession]:
    """Create a session for reviewing easy cards only."""
    filter_config = ReviewFilter(difficulty_max=difficulty_threshold)
    return create_filtered_review_session(deck_name, filter_config)


def create_new_cards_session(deck_name: str, limit: int = 20) -> Optional[ReviewSession]:
    """Create a session for reviewing new cards only."""
    filter_config = ReviewFilter(card_types=['new'])
    limits = SessionLimits(max_new_cards=limit, max_review_cards=0)
    return create_filtered_review_session(deck_name, filter_config, limits)


def create_overdue_cards_session(deck_name: str) -> Optional[ReviewSession]:
    """Create a session for reviewing overdue cards only."""
    filter_config = ReviewFilter(overdue_only=True)
    return create_filtered_review_session(deck_name, filter_config)


def create_learning_cards_session(deck_name: str) -> Optional[ReviewSession]:
    """Create a session for reviewing learning cards only."""
    filter_config = ReviewFilter(learning_states=['learning', 'relearning'])
    return create_filtered_review_session(deck_name, filter_config)
