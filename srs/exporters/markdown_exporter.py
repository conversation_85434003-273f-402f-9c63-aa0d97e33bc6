"""
Markdown exporter for the spaced repetition system.

This module provides functionality to export cards to Markdown files
with structured format for better integration with note-taking systems.
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models import Deck, Card

logger = logging.getLogger(__name__)


class MarkdownExporter:
    """
    Exports cards to Markdown files from the SRS system.
    
    Creates structured Markdown format with card metadata.
    """
    
    def __init__(self):
        """Initialize the Markdown exporter."""
        pass
    
    def export_deck(self, deck_name: str, output_path: str, 
                   include_metadata: bool = True, include_stats: bool = False) -> Dict[str, Any]:
        """
        Export deck to Markdown file.
        
        Args:
            deck_name: Name of the deck to export
            output_path: Path for the output Markdown file
            include_metadata: Include card metadata (difficulty, tags, etc.)
            include_stats: Include review statistics
            
        Returns:
            Dictionary with export results
        """
        logger.info(f"Starting Markdown export: {deck_name} -> {output_path}")
        
        try:
            # Get deck and cards
            deck = Deck.get_by_name(deck_name)
            if not deck:
                raise ValueError(f"Deck '{deck_name}' not found")
            
            cards = Card.get_by_deck(deck.id)
            
            # Generate Markdown content
            markdown_content = self._generate_markdown(deck, cards, include_metadata, include_stats)
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as file:
                file.write(markdown_content)
            
            return {
                'deck_name': deck_name,
                'output_path': output_path,
                'exported_cards': len(cards),
                'file_size': len(markdown_content.encode('utf-8'))
            }
            
        except Exception as e:
            logger.error(f"Markdown export failed: {e}")
            raise
    
    def _generate_markdown(self, deck: Deck, cards: List[Card], 
                          include_metadata: bool, include_stats: bool) -> str:
        """Generate Markdown content for the deck."""
        lines = []
        
        # Header
        lines.append(f"# Deck: {deck.name}")
        lines.append("")
        
        # Deck description
        if deck.description:
            lines.append(f"> {deck.description}")
            lines.append("")
        
        # Deck metadata
        if include_metadata:
            lines.append("## Deck Settings")
            lines.append("")
            lines.append("```yaml")
            lines.append(f"daily_new_limit: {deck.daily_new_limit}")
            lines.append(f"daily_review_limit: {deck.daily_review_limit}")
            
            if hasattr(deck, 'learning_steps') and deck.learning_steps:
                steps = [int(x) for x in deck.learning_steps.split()]
                lines.append(f"learning_steps: {steps}")
            
            if hasattr(deck, 'graduation_interval'):
                lines.append(f"graduation_interval: {deck.graduation_interval}")
            
            if hasattr(deck, 'easy_interval'):
                lines.append(f"easy_interval: {deck.easy_interval}")
            
            lines.append("```")
            lines.append("")
        
        # Deck statistics
        if include_stats:
            lines.append("## Statistics")
            lines.append("")
            
            total_cards = len(cards)
            new_cards = sum(1 for card in cards if card.repetitions == 0)
            learning_cards = sum(1 for card in cards if hasattr(card, 'learning_state') and card.learning_state in ['learning', 'relearning'])
            graduated_cards = sum(1 for card in cards if card.repetitions > 0)
            
            lines.append(f"- **Total Cards:** {total_cards}")
            lines.append(f"- **New Cards:** {new_cards}")
            lines.append(f"- **Learning Cards:** {learning_cards}")
            lines.append(f"- **Graduated Cards:** {graduated_cards}")
            
            if total_cards > 0:
                avg_difficulty = sum(card.difficulty for card in cards) / total_cards
                lines.append(f"- **Average Difficulty:** {avg_difficulty:.2f}")
            
            lines.append(f"- **Created:** {deck.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append(f"- **Exported:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append("")
        
        # Cards
        for i, card in enumerate(cards, 1):
            lines.extend(self._format_card(card, i, include_metadata, include_stats))
            lines.append("")
        
        return "\n".join(lines)
    
    def _format_card(self, card: Card, card_number: int, 
                    include_metadata: bool, include_stats: bool) -> List[str]:
        """Format a single card as Markdown."""
        lines = []
        
        # Card header
        lines.append(f"## Card {card_number}")
        lines.append("")
        
        # Front and back
        lines.append(f"**Front:** {self._escape_markdown(card.front)}")
        lines.append("")
        lines.append(f"**Back:** {self._escape_markdown(card.back)}")
        lines.append("")
        
        # Metadata
        if include_metadata:
            # Difficulty
            lines.append(f"**Difficulty:** {card.difficulty:.2f}")
            
            # Learning state
            if hasattr(card, 'learning_state'):
                lines.append(f"**Learning State:** {card.learning_state}")
            
            # Tags (placeholder for when tags system is implemented)
            # lines.append(f"**Tags:** {', '.join(card.tags)}")
            
            lines.append("")
        
        # Statistics
        if include_stats:
            lines.append("**Statistics:**")
            lines.append(f"- Repetitions: {card.repetitions}")
            lines.append(f"- Interval: {card.interval} days")
            lines.append(f"- Ease Factor: {card.ease_factor:.2f}")
            
            if hasattr(card, 'consecutive_correct'):
                lines.append(f"- Consecutive Correct: {card.consecutive_correct}")
            
            if hasattr(card, 'consecutive_incorrect'):
                lines.append(f"- Consecutive Incorrect: {card.consecutive_incorrect}")
            
            if card.due_date:
                lines.append(f"- Due Date: {card.due_date.strftime('%Y-%m-%d %H:%M:%S')}")
            
            lines.append(f"- Created: {card.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append("")
        
        return lines
    
    def _escape_markdown(self, text: str) -> str:
        """Escape special Markdown characters in text."""
        # Basic escaping for common Markdown characters
        text = text.replace('\\', '\\\\')
        text = text.replace('*', '\\*')
        text = text.replace('_', '\\_')
        text = text.replace('[', '\\[')
        text = text.replace(']', '\\]')
        text = text.replace('`', '\\`')
        text = text.replace('#', '\\#')
        
        return text
    
    def export_multiple_decks(self, deck_names: List[str], output_dir: str,
                             include_metadata: bool = True, include_stats: bool = False) -> Dict[str, Any]:
        """
        Export multiple decks to separate Markdown files.
        
        Args:
            deck_names: List of deck names to export
            output_dir: Directory for output files
            include_metadata: Include card metadata
            include_stats: Include review statistics
            
        Returns:
            Dictionary with export results
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = {
            'exported_decks': [],
            'failed_decks': [],
            'total_cards': 0
        }
        
        for deck_name in deck_names:
            try:
                # Generate safe filename
                safe_name = "".join(c for c in deck_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                filename = f"{safe_name}.md"
                file_path = output_path / filename
                
                result = self.export_deck(deck_name, str(file_path), include_metadata, include_stats)
                
                results['exported_decks'].append({
                    'deck_name': deck_name,
                    'file_path': str(file_path),
                    'cards': result['exported_cards']
                })
                results['total_cards'] += result['exported_cards']
                
            except Exception as e:
                logger.error(f"Failed to export deck '{deck_name}': {e}")
                results['failed_decks'].append({
                    'deck_name': deck_name,
                    'error': str(e)
                })
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported export formats."""
        return ['.md', '.markdown']
    
    def preview_export(self, deck_name: str, max_cards: int = 3) -> str:
        """
        Generate a preview of the Markdown export.
        
        Args:
            deck_name: Name of the deck to preview
            max_cards: Maximum number of cards to include in preview
            
        Returns:
            Preview Markdown content
        """
        deck = Deck.get_by_name(deck_name)
        if not deck:
            return f"# Error: Deck '{deck_name}' not found"
        
        cards = Card.get_by_deck(deck.id)
        preview_cards = cards[:max_cards]
        
        # Generate preview with limited cards
        content = self._generate_markdown(deck, preview_cards, True, True)
        
        if len(cards) > max_cards:
            content += f"\n\n*... and {len(cards) - max_cards} more cards*"
        
        return content
