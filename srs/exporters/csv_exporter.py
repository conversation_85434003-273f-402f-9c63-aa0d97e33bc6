"""
CSV exporter for the spaced repetition system.

This module provides functionality to export cards to CSV files
for compatibility with spreadsheet applications and other systems.
"""

import logging
import csv
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models import Deck, Card

logger = logging.getLogger(__name__)


class CSVExporter:
    """
    Exports cards to CSV files from the SRS system.
    
    Creates CSV format compatible with spreadsheet applications.
    """
    
    def __init__(self):
        """Initialize the CSV exporter."""
        pass
    
    def export_deck(self, deck_name: str, output_path: str, 
                   delimiter: str = ',', include_metadata: bool = True,
                   include_stats: bool = False) -> Dict[str, Any]:
        """
        Export deck to CSV file.
        
        Args:
            deck_name: Name of the deck to export
            output_path: Path for the output CSV file
            delimiter: CSV delimiter character
            include_metadata: Include card metadata (difficulty, learning state, etc.)
            include_stats: Include review statistics
            
        Returns:
            Dictionary with export results
        """
        logger.info(f"Starting CSV export: {deck_name} -> {output_path}")
        
        try:
            # Get deck and cards
            deck = Deck.get_by_name(deck_name)
            if not deck:
                raise ValueError(f"Deck '{deck_name}' not found")
            
            cards = Card.get_by_deck(deck.id)
            
            # Generate CSV content
            self._write_csv_file(output_path, deck, cards, delimiter, include_metadata, include_stats)
            
            return {
                'deck_name': deck_name,
                'output_path': output_path,
                'exported_cards': len(cards),
                'delimiter': delimiter
            }
            
        except Exception as e:
            logger.error(f"CSV export failed: {e}")
            raise
    
    def _write_csv_file(self, output_path: str, deck: Deck, cards: List[Card],
                       delimiter: str, include_metadata: bool, include_stats: bool):
        """Write cards to CSV file."""
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['front', 'back']
            
            if include_metadata:
                columns.extend(['difficulty', 'learning_state'])
            
            if include_stats:
                columns.extend([
                    'repetitions', 'interval', 'ease_factor',
                    'consecutive_correct', 'consecutive_incorrect',
                    'due_date', 'created_at'
                ])
            
            writer = csv.DictWriter(csvfile, fieldnames=columns, delimiter=delimiter)
            writer.writeheader()
            
            # Write cards
            for card in cards:
                row = {
                    'front': card.front,
                    'back': card.back
                }
                
                if include_metadata:
                    row['difficulty'] = card.difficulty
                    row['learning_state'] = getattr(card, 'learning_state', 'new')
                
                if include_stats:
                    row['repetitions'] = card.repetitions
                    row['interval'] = card.interval
                    row['ease_factor'] = card.ease_factor
                    row['consecutive_correct'] = getattr(card, 'consecutive_correct', 0)
                    row['consecutive_incorrect'] = getattr(card, 'consecutive_incorrect', 0)
                    row['due_date'] = card.due_date.isoformat() if card.due_date else ''
                    row['created_at'] = card.created_at.isoformat() if card.created_at else ''
                
                writer.writerow(row)
    
    def export_multiple_decks(self, deck_names: List[str], output_dir: str,
                             delimiter: str = ',', include_metadata: bool = True,
                             include_stats: bool = False) -> Dict[str, Any]:
        """
        Export multiple decks to separate CSV files.
        
        Args:
            deck_names: List of deck names to export
            output_dir: Directory for output files
            delimiter: CSV delimiter character
            include_metadata: Include card metadata
            include_stats: Include review statistics
            
        Returns:
            Dictionary with export results
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = {
            'exported_decks': [],
            'failed_decks': [],
            'total_cards': 0
        }
        
        for deck_name in deck_names:
            try:
                # Generate safe filename
                safe_name = "".join(c for c in deck_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                filename = f"{safe_name}.csv"
                file_path = output_path / filename
                
                result = self.export_deck(deck_name, str(file_path), delimiter, include_metadata, include_stats)
                
                results['exported_decks'].append({
                    'deck_name': deck_name,
                    'file_path': str(file_path),
                    'cards': result['exported_cards']
                })
                results['total_cards'] += result['exported_cards']
                
            except Exception as e:
                logger.error(f"Failed to export deck '{deck_name}': {e}")
                results['failed_decks'].append({
                    'deck_name': deck_name,
                    'error': str(e)
                })
        
        return results
    
    def export_all_decks_combined(self, output_path: str, delimiter: str = ',',
                                 include_deck_name: bool = True,
                                 include_metadata: bool = True,
                                 include_stats: bool = False) -> Dict[str, Any]:
        """
        Export all decks to a single CSV file.
        
        Args:
            output_path: Path for the output CSV file
            delimiter: CSV delimiter character
            include_deck_name: Include deck name as a column
            include_metadata: Include card metadata
            include_stats: Include review statistics
            
        Returns:
            Dictionary with export results
        """
        logger.info(f"Starting combined CSV export -> {output_path}")
        
        try:
            # Get all decks
            from ..database import get_database
            db = get_database()
            
            deck_results = db.execute_query("SELECT id, name FROM decks ORDER BY name")
            all_cards = []
            deck_count = 0
            
            for deck_row in deck_results:
                deck = Deck.get_by_id(deck_row['id'])
                if deck:
                    cards = Card.get_by_deck(deck.id)
                    for card in cards:
                        card._deck_name = deck.name  # Add deck name to card
                    all_cards.extend(cards)
                    deck_count += 1
            
            # Write combined CSV
            self._write_combined_csv_file(output_path, all_cards, delimiter, 
                                        include_deck_name, include_metadata, include_stats)
            
            return {
                'output_path': output_path,
                'exported_decks': deck_count,
                'exported_cards': len(all_cards),
                'delimiter': delimiter
            }
            
        except Exception as e:
            logger.error(f"Combined CSV export failed: {e}")
            raise
    
    def _write_combined_csv_file(self, output_path: str, cards: List[Card],
                                delimiter: str, include_deck_name: bool,
                                include_metadata: bool, include_stats: bool):
        """Write all cards to a single CSV file."""
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = []
            
            if include_deck_name:
                columns.append('deck_name')
            
            columns.extend(['front', 'back'])
            
            if include_metadata:
                columns.extend(['difficulty', 'learning_state'])
            
            if include_stats:
                columns.extend([
                    'repetitions', 'interval', 'ease_factor',
                    'consecutive_correct', 'consecutive_incorrect',
                    'due_date', 'created_at'
                ])
            
            writer = csv.DictWriter(csvfile, fieldnames=columns, delimiter=delimiter)
            writer.writeheader()
            
            # Write cards
            for card in cards:
                row = {}
                
                if include_deck_name:
                    row['deck_name'] = getattr(card, '_deck_name', 'Unknown')
                
                row.update({
                    'front': card.front,
                    'back': card.back
                })
                
                if include_metadata:
                    row['difficulty'] = card.difficulty
                    row['learning_state'] = getattr(card, 'learning_state', 'new')
                
                if include_stats:
                    row['repetitions'] = card.repetitions
                    row['interval'] = card.interval
                    row['ease_factor'] = card.ease_factor
                    row['consecutive_correct'] = getattr(card, 'consecutive_correct', 0)
                    row['consecutive_incorrect'] = getattr(card, 'consecutive_incorrect', 0)
                    row['due_date'] = card.due_date.isoformat() if card.due_date else ''
                    row['created_at'] = card.created_at.isoformat() if card.created_at else ''
                
                writer.writerow(row)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported export formats."""
        return ['.csv', '.tsv']
    
    def preview_export(self, deck_name: str, max_cards: int = 5,
                      delimiter: str = ',') -> List[List[str]]:
        """
        Generate a preview of the CSV export.
        
        Args:
            deck_name: Name of the deck to preview
            max_cards: Maximum number of cards to include in preview
            delimiter: CSV delimiter character
            
        Returns:
            List of CSV rows (including header)
        """
        deck = Deck.get_by_name(deck_name)
        if not deck:
            return [['Error', f"Deck '{deck_name}' not found"]]
        
        cards = Card.get_by_deck(deck.id)
        preview_cards = cards[:max_cards]
        
        # Generate preview rows
        rows = []
        
        # Header
        rows.append(['front', 'back', 'difficulty', 'repetitions', 'interval'])
        
        # Cards
        for card in preview_cards:
            rows.append([
                card.front,
                card.back,
                str(card.difficulty),
                str(card.repetitions),
                str(card.interval)
            ])
        
        if len(cards) > max_cards:
            rows.append(['...', f'and {len(cards) - max_cards} more cards', '...', '...', '...'])
        
        return rows
