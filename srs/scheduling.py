"""
Smart scheduling system for the spaced repetition system.

This module provides intelligent card selection, priority-based sorting,
and session management with daily limits and time-boxing support.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .models import Card, Deck
from .database import get_database

logger = logging.getLogger(__name__)


class CardPriority(Enum):
    """Card priority levels for scheduling."""
    OVERDUE = 1      # Cards overdue by more than 1 day
    DUE_TODAY = 2    # Cards due today
    NEW = 3          # New cards (never reviewed)
    FUTURE = 4       # Cards due in the future


@dataclass
class SessionLimits:
    """Session limits configuration."""
    max_cards: Optional[int] = None
    max_new_cards: Optional[int] = None
    max_review_cards: Optional[int] = None
    max_time_minutes: Optional[int] = None
    new_cards_only: bool = False
    due_cards_only: bool = False


@dataclass
class CardScore:
    """Card scoring for priority ordering."""
    card: Card
    priority: CardPriority
    days_overdue: float = 0.0
    difficulty_score: float = 0.0
    total_score: float = 0.0


class SmartScheduler:
    """
    Smart scheduler for intelligent card selection and session management.
    
    Provides priority-based card selection, daily limit enforcement,
    and time-boxed session support.
    """
    
    def __init__(self):
        """Initialize the smart scheduler."""
        self.db = get_database()
    
    def create_smart_session(self, deck: Deck, limits: SessionLimits) -> List[Card]:
        """
        Create a smart review session with intelligent card selection.
        
        Args:
            deck: Deck to create session for
            limits: Session limits configuration
            
        Returns:
            List of cards ordered by priority
        """
        # Get all available cards
        available_cards = self._get_available_cards(deck, limits)
        
        # Score and prioritize cards
        scored_cards = self._score_cards(available_cards)
        
        # Sort by priority and score
        sorted_cards = self._sort_cards_by_priority(scored_cards)
        
        # Apply limits
        limited_cards = self._apply_session_limits(sorted_cards, limits)
        
        logger.info(f"Created smart session for deck '{deck.name}': {len(limited_cards)} cards selected")
        return limited_cards
    
    def _get_available_cards(self, deck: Deck, limits: SessionLimits) -> List[Card]:
        """Get available cards based on session limits."""
        now = datetime.now()
        
        if limits.new_cards_only:
            # Only new cards
            query = """
            SELECT * FROM cards 
            WHERE deck_id = ? AND repetitions = 0
            ORDER BY created_at
            """
            params = (deck.id,)
        elif limits.due_cards_only:
            # Only due cards (excluding new cards)
            query = """
            SELECT * FROM cards 
            WHERE deck_id = ? AND repetitions > 0 AND due_date <= ?
            ORDER BY due_date
            """
            params = (deck.id, now.isoformat())
        else:
            # All cards (new and due)
            query = """
            SELECT * FROM cards 
            WHERE deck_id = ? AND (repetitions = 0 OR due_date <= ?)
            ORDER BY due_date, created_at
            """
            params = (deck.id, now.isoformat())
        
        results = self.db.execute_query(query, params)
        
        cards = []
        for row in results:
            card = Card(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                due_date=datetime.fromisoformat(row['due_date']),
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] if 'difficulty' in row.keys() else 0.5,
                last_review_duration=row['last_review_duration'] if 'last_review_duration' in row.keys() else 0,
                consecutive_correct=row['consecutive_correct'] if 'consecutive_correct' in row.keys() else 0,
                consecutive_incorrect=row['consecutive_incorrect'] if 'consecutive_incorrect' in row.keys() else 0
            )
            cards.append(card)
        
        return cards
    
    def _score_cards(self, cards: List[Card]) -> List[CardScore]:
        """Score cards for priority ordering."""
        now = datetime.now()
        scored_cards = []
        
        for card in cards:
            # Determine priority
            if card.repetitions == 0:
                priority = CardPriority.NEW
                days_overdue = 0.0
            else:
                days_diff = (now - card.due_date).total_seconds() / 86400  # Convert to days
                if days_diff > 1:
                    priority = CardPriority.OVERDUE
                    days_overdue = days_diff
                elif days_diff >= 0:
                    priority = CardPriority.DUE_TODAY
                    days_overdue = days_diff
                else:
                    priority = CardPriority.FUTURE
                    days_overdue = 0.0
            
            # Calculate difficulty score (higher = more difficult)
            difficulty_score = card.difficulty
            
            # Calculate total score for sorting
            # Lower score = higher priority
            base_score = priority.value * 1000
            overdue_penalty = days_overdue * 100  # More overdue = higher priority
            difficulty_bonus = difficulty_score * 10  # More difficult = higher priority
            
            total_score = base_score - overdue_penalty - difficulty_bonus
            
            scored_card = CardScore(
                card=card,
                priority=priority,
                days_overdue=days_overdue,
                difficulty_score=difficulty_score,
                total_score=total_score
            )
            scored_cards.append(scored_card)
        
        return scored_cards
    
    def _sort_cards_by_priority(self, scored_cards: List[CardScore]) -> List[Card]:
        """Sort cards by priority and score."""
        # Sort by total score (lower = higher priority)
        sorted_scored = sorted(scored_cards, key=lambda x: x.total_score)
        
        # Extract cards in priority order
        return [scored.card for scored in sorted_scored]
    
    def _apply_session_limits(self, cards: List[Card], limits: SessionLimits) -> List[Card]:
        """Apply session limits to card list."""
        limited_cards = []
        new_card_count = 0
        review_card_count = 0
        
        for card in cards:
            # Check overall card limit
            if limits.max_cards and len(limited_cards) >= limits.max_cards:
                break
            
            # Check new card limit
            if card.repetitions == 0:
                if limits.max_new_cards and new_card_count >= limits.max_new_cards:
                    continue
                new_card_count += 1
            else:
                # Check review card limit
                if limits.max_review_cards and review_card_count >= limits.max_review_cards:
                    continue
                review_card_count += 1
            
            limited_cards.append(card)
        
        logger.debug(f"Applied limits: {len(limited_cards)} total, {new_card_count} new, {review_card_count} review")
        return limited_cards
    
    def estimate_session_time(self, cards: List[Card]) -> int:
        """
        Estimate session time in minutes.
        
        Args:
            cards: List of cards for the session
            
        Returns:
            Estimated time in minutes
        """
        if not cards:
            return 0
        
        # Base time estimates (in seconds)
        NEW_CARD_TIME = 30      # 30 seconds for new cards
        REVIEW_CARD_TIME = 15   # 15 seconds for review cards
        DIFFICULT_CARD_BONUS = 10  # Extra time for difficult cards
        
        total_seconds = 0
        
        for card in cards:
            if card.repetitions == 0:
                # New card
                base_time = NEW_CARD_TIME
            else:
                # Review card
                base_time = REVIEW_CARD_TIME
            
            # Add difficulty bonus
            difficulty_bonus = card.difficulty * DIFFICULT_CARD_BONUS
            card_time = base_time + difficulty_bonus
            
            total_seconds += card_time
        
        # Convert to minutes and round up
        estimated_minutes = int((total_seconds + 59) // 60)
        
        logger.debug(f"Estimated session time: {estimated_minutes} minutes for {len(cards)} cards")
        return estimated_minutes
    
    def create_time_boxed_session(self, deck: Deck, max_time_minutes: int, 
                                 limits: Optional[SessionLimits] = None) -> List[Card]:
        """
        Create a time-boxed session that fits within the specified time limit.
        
        Args:
            deck: Deck to create session for
            max_time_minutes: Maximum session time in minutes
            limits: Additional session limits
            
        Returns:
            List of cards that fit within the time limit
        """
        if limits is None:
            limits = SessionLimits()
        
        # Get all available cards
        available_cards = self._get_available_cards(deck, limits)
        
        # Score and prioritize cards
        scored_cards = self._score_cards(available_cards)
        sorted_cards = self._sort_cards_by_priority(scored_cards)
        
        # Select cards that fit within time limit
        selected_cards = []
        estimated_time = 0
        
        for card in sorted_cards:
            # Estimate time for this card
            if card.repetitions == 0:
                card_time = 0.5  # 30 seconds for new cards
            else:
                card_time = 0.25  # 15 seconds for review cards
            
            card_time += card.difficulty * 0.17  # Difficulty bonus (10 seconds)
            
            # Check if adding this card would exceed time limit
            if estimated_time + card_time > max_time_minutes:
                break
            
            selected_cards.append(card)
            estimated_time += card_time
            
            # Apply other limits
            if limits.max_cards and len(selected_cards) >= limits.max_cards:
                break
        
        logger.info(f"Created time-boxed session: {len(selected_cards)} cards, ~{estimated_time:.1f} minutes")
        return selected_cards
    
    def get_session_statistics(self, cards: List[Card]) -> Dict[str, Any]:
        """
        Get statistics for a session.
        
        Args:
            cards: List of cards in the session
            
        Returns:
            Dictionary with session statistics
        """
        if not cards:
            return {
                'total_cards': 0,
                'new_cards': 0,
                'review_cards': 0,
                'overdue_cards': 0,
                'estimated_time_minutes': 0,
                'average_difficulty': 0.0
            }
        
        now = datetime.now()
        new_cards = sum(1 for card in cards if card.repetitions == 0)
        review_cards = len(cards) - new_cards
        overdue_cards = sum(1 for card in cards 
                          if card.repetitions > 0 and card.due_date < now)
        
        estimated_time = self.estimate_session_time(cards)
        average_difficulty = sum(card.difficulty for card in cards) / len(cards)
        
        return {
            'total_cards': len(cards),
            'new_cards': new_cards,
            'review_cards': review_cards,
            'overdue_cards': overdue_cards,
            'estimated_time_minutes': estimated_time,
            'average_difficulty': average_difficulty
        }
