"""
Progress tracking and achievement system for the spaced repetition system.

This module provides streak tracking, achievement management, and motivational
features to encourage consistent study habits.
"""

import json
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from .database import get_database

logger = logging.getLogger(__name__)


@dataclass
class UserStreak:
    """User streak data structure."""
    id: Optional[int] = None
    user_id: str = 'default'
    streak_type: str = ''
    current_streak: int = 0
    longest_streak: int = 0
    last_activity_date: Optional[date] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class Achievement:
    """Achievement data structure."""
    id: Optional[int] = None
    name: str = ''
    description: str = ''
    criteria: Dict[str, Any] = None
    icon: str = '🏆'
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.criteria is None:
            self.criteria = {}
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class UserAchievement:
    """User achievement unlock data structure."""
    id: Optional[int] = None
    user_id: str = 'default'
    achievement_id: int = 0
    unlocked_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.unlocked_at is None:
            self.unlocked_at = datetime.now()


class ProgressTracker:
    """
    Progress tracking system for managing streaks and achievements.
    
    Provides methods for tracking study streaks, managing achievements,
    and providing motivational feedback to users.
    """
    
    def __init__(self):
        """Initialize the progress tracker."""
        self.db = get_database()
        self._initialize_default_achievements()
    
    def _initialize_default_achievements(self):
        """Initialize default achievements if they don't exist."""
        default_achievements = [
            {
                'name': 'First Steps',
                'description': 'Complete your first review session',
                'criteria': {'reviews_completed': 1},
                'icon': '👶'
            },
            {
                'name': 'Getting Started',
                'description': 'Review 10 cards',
                'criteria': {'total_reviews': 10},
                'icon': '🌱'
            },
            {
                'name': 'Dedicated Learner',
                'description': 'Review 100 cards',
                'criteria': {'total_reviews': 100},
                'icon': '📚'
            },
            {
                'name': 'Study Streak',
                'description': 'Maintain a 7-day study streak',
                'criteria': {'daily_streak': 7},
                'icon': '🔥'
            },
            {
                'name': 'Consistency Master',
                'description': 'Maintain a 30-day study streak',
                'criteria': {'daily_streak': 30},
                'icon': '💎'
            },
            {
                'name': 'Perfect Session',
                'description': 'Complete a session with all ratings 3 or 4',
                'criteria': {'perfect_session': True},
                'icon': '⭐'
            },
            {
                'name': 'Speed Learner',
                'description': 'Review 50 cards in a single session',
                'criteria': {'session_cards': 50},
                'icon': '⚡'
            },
            {
                'name': 'Knowledge Seeker',
                'description': 'Learn 100 new cards',
                'criteria': {'new_cards_learned': 100},
                'icon': '🎓'
            }
        ]
        
        for achievement_data in default_achievements:
            if not self._achievement_exists(achievement_data['name']):
                self.create_achievement(
                    name=achievement_data['name'],
                    description=achievement_data['description'],
                    criteria=achievement_data['criteria'],
                    icon=achievement_data['icon']
                )
    
    def _achievement_exists(self, name: str) -> bool:
        """Check if an achievement already exists."""
        results = self.db.execute_query(
            "SELECT id FROM achievements WHERE name = ?", (name,)
        )
        return len(results) > 0
    
    def create_achievement(self, name: str, description: str, criteria: Dict[str, Any], icon: str = '🏆') -> int:
        """
        Create a new achievement.
        
        Args:
            name: Achievement name
            description: Achievement description
            criteria: Achievement criteria as dictionary
            icon: Achievement icon
            
        Returns:
            Achievement ID
        """
        achievement_id = self.db.execute_update(
            """INSERT INTO achievements (name, description, criteria, icon)
               VALUES (?, ?, ?, ?)""",
            (name, description, json.dumps(criteria), icon)
        )
        
        logger.debug(f"Created achievement '{name}' with ID {achievement_id}")
        return achievement_id
    
    def get_user_streak(self, streak_type: str, user_id: str = 'default') -> Optional[UserStreak]:
        """
        Get user streak for a specific type.
        
        Args:
            streak_type: Type of streak (daily_review, new_cards, perfect_session)
            user_id: User ID
            
        Returns:
            UserStreak object or None if not found
        """
        results = self.db.execute_query(
            """SELECT * FROM user_streaks 
               WHERE user_id = ? AND streak_type = ?""",
            (user_id, streak_type)
        )
        
        if not results:
            return None
        
        row = results[0]
        return UserStreak(
            id=row['id'],
            user_id=row['user_id'],
            streak_type=row['streak_type'],
            current_streak=row['current_streak'],
            longest_streak=row['longest_streak'],
            last_activity_date=datetime.fromisoformat(row['last_activity_date']).date() if row['last_activity_date'] else None,
            created_at=datetime.fromisoformat(row['created_at'])
        )
    
    def update_streak(self, streak_type: str, activity_date: date = None, user_id: str = 'default'):
        """
        Update user streak for a specific type.
        
        Args:
            streak_type: Type of streak
            activity_date: Date of activity (default: today)
            user_id: User ID
        """
        if activity_date is None:
            activity_date = date.today()
        
        # Get existing streak
        streak = self.get_user_streak(streak_type, user_id)
        
        if streak is None:
            # Create new streak
            streak = UserStreak(
                user_id=user_id,
                streak_type=streak_type,
                current_streak=1,
                longest_streak=1,
                last_activity_date=activity_date
            )
            
            self.db.execute_update(
                """INSERT INTO user_streaks 
                   (user_id, streak_type, current_streak, longest_streak, last_activity_date)
                   VALUES (?, ?, ?, ?, ?)""",
                (streak.user_id, streak.streak_type, streak.current_streak,
                 streak.longest_streak, streak.last_activity_date.isoformat())
            )
        else:
            # Update existing streak
            if streak.last_activity_date:
                days_diff = (activity_date - streak.last_activity_date).days
                
                if days_diff == 1:
                    # Consecutive day - increment streak
                    streak.current_streak += 1
                elif days_diff == 0:
                    # Same day - no change to streak
                    pass
                else:
                    # Gap in activity - reset streak
                    streak.current_streak = 1
            else:
                # First activity
                streak.current_streak = 1
            
            # Update longest streak if current is longer
            if streak.current_streak > streak.longest_streak:
                streak.longest_streak = streak.current_streak
            
            streak.last_activity_date = activity_date
            
            self.db.execute_update(
                """UPDATE user_streaks 
                   SET current_streak = ?, longest_streak = ?, last_activity_date = ?
                   WHERE id = ?""",
                (streak.current_streak, streak.longest_streak,
                 streak.last_activity_date.isoformat(), streak.id)
            )
        
        logger.debug(f"Updated {streak_type} streak to {streak.current_streak} for user {user_id}")
        
        # Check for streak-related achievements
        self._check_streak_achievements(user_id, streak_type, streak.current_streak)
    
    def _check_streak_achievements(self, user_id: str, streak_type: str, current_streak: int):
        """Check and unlock streak-related achievements."""
        if streak_type == 'daily_review':
            # Check for daily streak achievements
            if current_streak >= 7:
                self._unlock_achievement_if_not_unlocked(user_id, 'Study Streak')
            if current_streak >= 30:
                self._unlock_achievement_if_not_unlocked(user_id, 'Consistency Master')
    
    def check_review_achievements(self, user_id: str = 'default'):
        """
        Check and unlock review-based achievements.
        
        Args:
            user_id: User ID
        """
        # Get total review count
        results = self.db.execute_query(
            """SELECT COUNT(*) as total_reviews FROM reviews r
               JOIN cards c ON r.card_id = c.id
               JOIN decks d ON c.deck_id = d.id""",
            ()
        )
        
        total_reviews = results[0]['total_reviews'] if results else 0
        
        # Check review count achievements
        if total_reviews >= 1:
            self._unlock_achievement_if_not_unlocked(user_id, 'First Steps')
        if total_reviews >= 10:
            self._unlock_achievement_if_not_unlocked(user_id, 'Getting Started')
        if total_reviews >= 100:
            self._unlock_achievement_if_not_unlocked(user_id, 'Dedicated Learner')
        
        # Get new cards learned count
        results = self.db.execute_query(
            """SELECT COUNT(DISTINCT r.card_id) as new_cards_learned 
               FROM reviews r
               JOIN cards c ON r.card_id = c.id
               WHERE c.repetitions = 1""",
            ()
        )
        
        new_cards_learned = results[0]['new_cards_learned'] if results else 0
        
        if new_cards_learned >= 100:
            self._unlock_achievement_if_not_unlocked(user_id, 'Knowledge Seeker')
    
    def check_session_achievements(self, session_stats: Dict[str, Any], user_id: str = 'default'):
        """
        Check and unlock session-based achievements.
        
        Args:
            session_stats: Session statistics dictionary
            user_id: User ID
        """
        cards_reviewed = session_stats.get('cards_reviewed', 0)
        rating_counts = session_stats.get('rating_counts', {})
        
        # Check for speed learner achievement
        if cards_reviewed >= 50:
            self._unlock_achievement_if_not_unlocked(user_id, 'Speed Learner')
        
        # Check for perfect session achievement
        total_reviews = sum(rating_counts.values())
        good_reviews = rating_counts.get(3, 0) + rating_counts.get(4, 0)
        
        if total_reviews > 0 and good_reviews == total_reviews:
            self._unlock_achievement_if_not_unlocked(user_id, 'Perfect Session')
    
    def _unlock_achievement_if_not_unlocked(self, user_id: str, achievement_name: str):
        """Unlock achievement if not already unlocked."""
        # Check if already unlocked
        results = self.db.execute_query(
            """SELECT ua.id FROM user_achievements ua
               JOIN achievements a ON ua.achievement_id = a.id
               WHERE ua.user_id = ? AND a.name = ?""",
            (user_id, achievement_name)
        )
        
        if results:
            return  # Already unlocked
        
        # Get achievement ID
        results = self.db.execute_query(
            "SELECT id FROM achievements WHERE name = ?", (achievement_name,)
        )
        
        if not results:
            return  # Achievement doesn't exist
        
        achievement_id = results[0]['id']
        
        # Unlock achievement
        self.db.execute_update(
            """INSERT INTO user_achievements (user_id, achievement_id)
               VALUES (?, ?)""",
            (user_id, achievement_id)
        )
        
        logger.info(f"🎉 Achievement unlocked for {user_id}: {achievement_name}")
    
    def get_user_achievements(self, user_id: str = 'default') -> List[Dict[str, Any]]:
        """
        Get all achievements unlocked by a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of achievement dictionaries
        """
        results = self.db.execute_query(
            """SELECT a.name, a.description, a.icon, ua.unlocked_at
               FROM user_achievements ua
               JOIN achievements a ON ua.achievement_id = a.id
               WHERE ua.user_id = ?
               ORDER BY ua.unlocked_at DESC""",
            (user_id,)
        )
        
        return [
            {
                'name': row['name'],
                'description': row['description'],
                'icon': row['icon'],
                'unlocked_at': datetime.fromisoformat(row['unlocked_at'])
            }
            for row in results
        ]
    
    def get_progress_summary(self, user_id: str = 'default') -> Dict[str, Any]:
        """
        Get a comprehensive progress summary for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary containing progress information
        """
        # Get streaks
        daily_streak = self.get_user_streak('daily_review', user_id)
        
        # Get achievements
        achievements = self.get_user_achievements(user_id)
        
        # Get total achievements available
        total_achievements_results = self.db.execute_query(
            "SELECT COUNT(*) as total FROM achievements", ()
        )
        total_achievements = total_achievements_results[0]['total'] if total_achievements_results else 0
        
        return {
            'daily_streak': daily_streak.current_streak if daily_streak else 0,
            'longest_daily_streak': daily_streak.longest_streak if daily_streak else 0,
            'achievements_unlocked': len(achievements),
            'total_achievements': total_achievements,
            'achievement_percentage': (len(achievements) / total_achievements * 100) if total_achievements > 0 else 0,
            'recent_achievements': achievements[:3]  # Last 3 achievements
        }
