"""
Spaced Repetition System (SRS) - A terminal-based flashcard application.

This package implements a spaced repetition learning system with the SM-2 algorithm,
SQLite persistence, and a command-line interface.
"""

__version__ = "0.1.0"
__author__ = "jachian22"

# Import main components for easy access
from .config import get_config, load_config, setup_logging
from .database import get_database, reset_database
from .models import Deck, Card, Review
from .algorithm import get_algorithm, SM2Config
from .review import ReviewSession, create_review_session, create_smart_review_session, create_time_boxed_session
from .scheduling import SmartScheduler, SessionLimits, CardPriority
from .graduation import GraduationEngine, LearningResponse
from .importers import AnkiImporter, CSVImporter, MarkdownImporter
from .exporters import MarkdownExporter, CSVExporter
from .search import SearchEngine, SearchResult, SearchQuery
from .statistics import StatisticsEngine, DailyStats, DeckStats
from .progress import ProgressTracker, UserStreak, Achievement, UserAchievement
from .visualization import (
    create_progress_bar, create_horizontal_bar_chart, create_line_chart,
    create_summary_box, create_achievement_display, export_data_to_csv
)
from .utils import (
    validate_deck_name,
    validate_card_content,
    parse_csv_file,
    export_cards_to_csv,
    format_duration,
    format_datetime,
    format_card_counts
)

# Initialize logging on import
import logging
logger = logging.getLogger(__name__)

def initialize_srs(config_path=None):
    """
    Initialize the SRS application with configuration and logging.

    Args:
        config_path: Optional path to configuration file
    """
    # Load configuration
    config = load_config(config_path)

    # Set up logging
    setup_logging(config)

    # Initialize database
    get_database(config.database_path)

    logger.info(f"SRS initialized - version {__version__}")
    return config

__all__ = [
    # Core components
    'Deck', 'Card', 'Review',
    'ReviewSession', 'create_review_session', 'create_smart_review_session', 'create_time_boxed_session',
    'SM2Config',

    # Scheduling
    'SmartScheduler', 'SessionLimits', 'CardPriority',

    # Graduation
    'GraduationEngine', 'LearningResponse',

    # Import/Export
    'AnkiImporter', 'CSVImporter', 'MarkdownImporter',
    'MarkdownExporter', 'CSVExporter',

    # Search
    'SearchEngine', 'SearchResult', 'SearchQuery',

    # Statistics
    'StatisticsEngine', 'DailyStats', 'DeckStats',

    # Progress tracking
    'ProgressTracker', 'UserStreak', 'Achievement', 'UserAchievement',

    # Visualization
    'create_progress_bar', 'create_horizontal_bar_chart', 'create_line_chart',
    'create_summary_box', 'create_achievement_display', 'export_data_to_csv',

    # Database and config
    'get_database', 'reset_database',
    'get_config', 'load_config', 'setup_logging',
    'get_algorithm',

    # Utilities
    'validate_deck_name', 'validate_card_content',
    'parse_csv_file', 'export_cards_to_csv',
    'format_duration', 'format_datetime', 'format_card_counts',

    # Initialization
    'initialize_srs',

    # Metadata
    '__version__', '__author__'
]