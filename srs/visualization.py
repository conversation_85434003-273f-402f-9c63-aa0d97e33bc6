"""
Visualization utilities for the spaced repetition system.

This module provides ASCII chart generators, progress bars, and terminal
output formatting for statistics and progress display.
"""

import csv
import math
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path


def create_progress_bar(current: int, total: int, width: int = 40, 
                       filled_char: str = '█', empty_char: str = '░') -> str:
    """
    Create an ASCII progress bar.
    
    Args:
        current: Current progress value
        total: Total/maximum value
        width: Width of the progress bar in characters
        filled_char: Character for filled portion
        empty_char: Character for empty portion
        
    Returns:
        ASCII progress bar string
    """
    if total == 0:
        percentage = 0
    else:
        percentage = min(100, max(0, (current / total) * 100))
    
    filled_width = int((percentage / 100) * width)
    empty_width = width - filled_width
    
    bar = filled_char * filled_width + empty_char * empty_width
    return f"[{bar}] {percentage:.1f}% ({current}/{total})"


def create_horizontal_bar_chart(data: Dict[str, int], max_width: int = 50,
                               title: str = "") -> str:
    """
    Create a horizontal ASCII bar chart.
    
    Args:
        data: Dictionary of label -> value pairs
        max_width: Maximum width for the longest bar
        title: Optional chart title
        
    Returns:
        ASCII bar chart string
    """
    if not data:
        return "No data to display"
    
    lines = []
    
    if title:
        lines.append(title)
        lines.append("=" * len(title))
        lines.append("")
    
    max_value = max(data.values()) if data.values() else 1
    max_label_width = max(len(str(label)) for label in data.keys())
    
    for label, value in data.items():
        # Calculate bar width proportional to value
        bar_width = int((value / max_value) * max_width) if max_value > 0 else 0
        bar = "█" * bar_width
        
        # Format the line with right-aligned label and value
        label_str = str(label).rjust(max_label_width)
        lines.append(f"{label_str} │{bar} {value}")
    
    return "\n".join(lines)


def create_line_chart(data: List[Tuple[str, float]], height: int = 10,
                     width: int = 60, title: str = "") -> str:
    """
    Create an ASCII line chart.
    
    Args:
        data: List of (label, value) tuples
        height: Height of the chart in characters
        width: Width of the chart in characters
        title: Optional chart title
        
    Returns:
        ASCII line chart string
    """
    if not data:
        return "No data to display"
    
    lines = []
    
    if title:
        lines.append(title)
        lines.append("=" * len(title))
        lines.append("")
    
    values = [value for _, value in data]
    labels = [label for label, _ in data]
    
    if not values:
        return "No data to display"
    
    min_val = min(values)
    max_val = max(values)
    
    # Avoid division by zero
    if max_val == min_val:
        max_val = min_val + 1
    
    # Create the chart grid
    chart = [[' ' for _ in range(width)] for _ in range(height)]
    
    # Plot the data points
    for i, (label, value) in enumerate(data):
        x = int((i / (len(data) - 1)) * (width - 1)) if len(data) > 1 else 0
        y = height - 1 - int(((value - min_val) / (max_val - min_val)) * (height - 1))
        
        if 0 <= x < width and 0 <= y < height:
            chart[y][x] = '●'
            
            # Connect points with lines
            if i > 0:
                prev_x = int(((i - 1) / (len(data) - 1)) * (width - 1)) if len(data) > 1 else 0
                prev_value = values[i - 1]
                prev_y = height - 1 - int(((prev_value - min_val) / (max_val - min_val)) * (height - 1))
                
                # Simple line drawing between points
                if prev_x != x:
                    for line_x in range(min(prev_x, x) + 1, max(prev_x, x)):
                        if 0 <= line_x < width:
                            # Linear interpolation for y
                            t = (line_x - prev_x) / (x - prev_x) if x != prev_x else 0
                            line_y = int(prev_y + t * (y - prev_y))
                            if 0 <= line_y < height and chart[line_y][line_x] == ' ':
                                chart[line_y][line_x] = '─'
    
    # Convert chart to string
    chart_lines = [''.join(row) for row in chart]
    
    # Add y-axis labels
    for i, line in enumerate(chart_lines):
        y_value = max_val - (i / (height - 1)) * (max_val - min_val)
        chart_lines[i] = f"{y_value:6.1f} │ {line}"
    
    lines.extend(chart_lines)
    
    # Add x-axis
    x_axis = "       └" + "─" * width
    lines.append(x_axis)
    
    # Add x-axis labels (simplified)
    if len(labels) <= 10:  # Only show labels if not too many
        x_labels = "        "
        for i, label in enumerate(labels):
            if i % max(1, len(labels) // 5) == 0:  # Show every nth label
                x_pos = int((i / (len(data) - 1)) * width) if len(data) > 1 else 0
                while len(x_labels) < 8 + x_pos:
                    x_labels += " "
                x_labels += label[:8]  # Truncate long labels
        lines.append(x_labels)
    
    return "\n".join(lines)


def create_histogram(data: List[float], bins: int = 10, width: int = 50,
                    title: str = "") -> str:
    """
    Create an ASCII histogram.
    
    Args:
        data: List of numeric values
        bins: Number of histogram bins
        width: Maximum width for bars
        title: Optional chart title
        
    Returns:
        ASCII histogram string
    """
    if not data:
        return "No data to display"
    
    lines = []
    
    if title:
        lines.append(title)
        lines.append("=" * len(title))
        lines.append("")
    
    min_val = min(data)
    max_val = max(data)
    
    if min_val == max_val:
        lines.append(f"All values are {min_val}")
        return "\n".join(lines)
    
    # Create bins
    bin_width = (max_val - min_val) / bins
    bin_counts = [0] * bins
    
    for value in data:
        bin_index = min(bins - 1, int((value - min_val) / bin_width))
        bin_counts[bin_index] += 1
    
    max_count = max(bin_counts) if bin_counts else 1
    
    # Create histogram bars
    for i, count in enumerate(bin_counts):
        bin_start = min_val + i * bin_width
        bin_end = min_val + (i + 1) * bin_width
        
        bar_length = int((count / max_count) * width) if max_count > 0 else 0
        bar = "█" * bar_length
        
        lines.append(f"{bin_start:6.1f}-{bin_end:6.1f} │{bar} {count}")
    
    return "\n".join(lines)


def format_duration(seconds: int) -> str:
    """
    Format duration in seconds to human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours}h {remaining_minutes}m"


def format_percentage(value: float, total: float) -> str:
    """
    Format a percentage with proper handling of edge cases.
    
    Args:
        value: Numerator value
        total: Denominator value
        
    Returns:
        Formatted percentage string
    """
    if total == 0:
        return "0.0%"
    percentage = (value / total) * 100
    return f"{percentage:.1f}%"


def export_data_to_csv(data: List[Dict[str, Any]], filepath: str) -> bool:
    """
    Export data to CSV file.
    
    Args:
        data: List of dictionaries to export
        filepath: Path to output CSV file
        
    Returns:
        True if successful, False otherwise
    """
    if not data:
        return False
    
    try:
        # Ensure directory exists
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # Get all unique keys from all dictionaries
        fieldnames = set()
        for row in data:
            fieldnames.update(row.keys())
        fieldnames = sorted(fieldnames)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        return True
    except Exception:
        return False


def create_summary_box(title: str, stats: Dict[str, Any], width: int = 60) -> str:
    """
    Create a formatted summary box with statistics.
    
    Args:
        title: Box title
        stats: Dictionary of statistic name -> value pairs
        width: Box width in characters
        
    Returns:
        Formatted summary box string
    """
    lines = []
    
    # Top border
    lines.append("┌" + "─" * (width - 2) + "┐")
    
    # Title
    title_line = f"│ {title.center(width - 4)} │"
    lines.append(title_line)
    
    # Separator
    lines.append("├" + "─" * (width - 2) + "┤")
    
    # Statistics
    for name, value in stats.items():
        if isinstance(value, float):
            value_str = f"{value:.2f}"
        elif isinstance(value, int):
            value_str = str(value)
        else:
            value_str = str(value)
        
        stat_line = f"│ {name:<{width-10}} {value_str:>6} │"
        lines.append(stat_line)
    
    # Bottom border
    lines.append("└" + "─" * (width - 2) + "┘")
    
    return "\n".join(lines)


def colorize_text(text: str, color: str) -> str:
    """
    Add ANSI color codes to text for terminal display.
    
    Args:
        text: Text to colorize
        color: Color name (red, green, yellow, blue, magenta, cyan, white)
        
    Returns:
        Colorized text string
    """
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'magenta': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'bold': '\033[1m',
        'reset': '\033[0m'
    }
    
    color_code = colors.get(color.lower(), '')
    reset_code = colors['reset']
    
    return f"{color_code}{text}{reset_code}"


def create_achievement_display(achievements: List[Dict[str, Any]]) -> str:
    """
    Create a formatted display of achievements.
    
    Args:
        achievements: List of achievement dictionaries
        
    Returns:
        Formatted achievement display string
    """
    if not achievements:
        return "No achievements unlocked yet."
    
    lines = []
    lines.append("🏆 Recent Achievements")
    lines.append("=" * 20)
    lines.append("")
    
    for achievement in achievements[:5]:  # Show last 5 achievements
        icon = achievement.get('icon', '🏆')
        name = achievement.get('name', 'Unknown')
        description = achievement.get('description', '')
        unlocked_at = achievement.get('unlocked_at')
        
        lines.append(f"{icon} {name}")
        if description:
            lines.append(f"   {description}")
        if unlocked_at:
            if isinstance(unlocked_at, datetime):
                date_str = unlocked_at.strftime("%Y-%m-%d")
            else:
                date_str = str(unlocked_at)
            lines.append(f"   Unlocked: {date_str}")
        lines.append("")
    
    return "\n".join(lines)
