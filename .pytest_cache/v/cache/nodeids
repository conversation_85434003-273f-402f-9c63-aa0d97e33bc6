["tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_singleton", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_with_custom_config", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_reset_algorithm", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization_with_custom_config", "tests/test_algorithm.py::TestSM2Algorithm::test_due_date_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_ease_factor_adjustments", "tests/test_algorithm.py::TestSM2Algorithm::test_easy_bonus_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_fuzz_factor_application", "tests/test_algorithm.py::TestSM2Algorithm::test_interval_bounds_enforcement", "tests/test_algorithm.py::TestSM2Algorithm::test_invalid_rating_raises_error", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_failure", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_steps_progression", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_1", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_2", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_3", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_4", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_repetitions", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_low_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_zero_interval_card", "tests/test_algorithm.py::TestSM2Config::test_custom_config", "tests/test_algorithm.py::TestSM2Config::test_default_config", "tests/test_anki_import.py::TestAnkiImporter::test_actual_import", "tests/test_anki_import.py::TestAnkiImporter::test_anki_importer_initialization", "tests/test_anki_import.py::TestAnkiImporter::test_clean_html", "tests/test_anki_import.py::TestAnkiImporter::test_cleanup", "tests/test_anki_import.py::TestAnkiImporter::test_convert_scheduling_data", "tests/test_anki_import.py::TestAnkiImporter::test_dry_run_import", "tests/test_anki_import.py::TestAnkiImporter::test_extract_apkg", "tests/test_anki_import.py::TestAnkiImporter::test_extract_card_content", "tests/test_anki_import.py::TestAnkiImporter::test_extract_card_content_insufficient_fields", "tests/test_anki_import.py::TestAnkiImporter::test_get_supported_formats", "tests/test_anki_import.py::TestAnkiImporter::test_import_with_merge", "tests/test_anki_import.py::TestAnkiImporter::test_import_without_merge_existing_deck", "tests/test_anki_import.py::TestAnkiImporter::test_parse_anki_database", "tests/test_anki_import.py::TestAnkiImporter::test_validate_file_invalid_apkg", "tests/test_anki_import.py::TestAnkiImporter::test_validate_file_invalid_extension", "tests/test_anki_import.py::TestAnkiImporter::test_validate_file_nonexistent", "tests/test_anki_import.py::TestAnkiImporter::test_validate_file_valid_apkg", "tests/test_cli.py::TestArgumentParser::test_command_parsing", "tests/test_cli.py::TestArgumentParser::test_help_argument", "tests/test_cli.py::TestArgumentParser::test_invalid_command", "tests/test_cli.py::TestArgumentParser::test_parser_setup", "tests/test_cli.py::TestArgumentParser::test_version_argument", "tests/test_cli.py::TestCLICommands::test_add_card_invalid_content", "tests/test_cli.py::TestCLICommands::test_add_card_nonexistent_deck", "tests/test_cli.py::TestCLICommands::test_add_card_with_arguments", "tests/test_cli.py::TestCLICommands::test_create_deck_duplicate", "tests/test_cli.py::TestCLICommands::test_create_deck_invalid_name", "tests/test_cli.py::TestCLICommands::test_create_deck_success", "tests/test_cli.py::TestCLICommands::test_delete_deck_nonexistent", "tests/test_cli.py::TestCLICommands::test_delete_deck_success", "tests/test_cli.py::TestCLICommands::test_list_cards_due_only", "tests/test_cli.py::TestCLICommands::test_list_cards_empty_deck", "tests/test_cli.py::TestCLICommands::test_list_cards_with_cards", "tests/test_cli.py::TestCLICommands::test_list_cards_with_limit", "tests/test_cli.py::TestCLICommands::test_list_decks_detailed", "tests/test_cli.py::TestCLICommands::test_list_decks_empty", "tests/test_cli.py::TestCLICommands::test_list_decks_with_decks", "tests/test_cli.py::TestCLICommands::test_status_detailed", "tests/test_cli.py::TestCLICommands::test_status_empty", "tests/test_cli.py::TestCLICommands::test_status_with_decks", "tests/test_cli.py::TestCLIEdgeCases::test_command_with_unicode_arguments", "tests/test_cli.py::TestCLIEdgeCases::test_empty_arguments", "tests/test_cli.py::TestCLIEdgeCases::test_keyboard_interrupt_handling", "tests/test_cli.py::TestCLIEdgeCases::test_unexpected_exception_handling", "tests/test_cli.py::TestCLIEdgeCases::test_very_long_arguments", "tests/test_cli.py::TestCLIErrorHandling::test_cli_initialization_failure", "tests/test_cli.py::TestCLIErrorHandling::test_cli_keyboard_interrupt", "tests/test_cli.py::TestCLIErrorHandling::test_cli_no_command", "tests/test_cli.py::TestCLIErrorHandling::test_cli_unknown_command", "tests/test_cli.py::TestCLIErrorHandling::test_initialization_failure", "tests/test_cli.py::TestCLIErrorHandling::test_invalid_command", "tests/test_cli.py::TestCLIErrorHandling::test_no_command", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_add_card_interactive_mode", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_config_file_argument", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_delete_deck_cancelled", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_delete_deck_with_confirmation", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_actual", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_dry_run", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_file_not_found", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_review_command_no_due_cards", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_review_command_with_limit", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_verbose_flag", "tests/test_cli.py::TestCLIUIHelpers::test_confirm_action_with_mock", "tests/test_cli.py::TestCLIUIHelpers::test_get_user_input_with_mock", "tests/test_cli.py::TestCLIUIHelpers::test_print_functions", "tests/test_cli.py::TestCLIUIHelpers::test_setup_signal_handlers", "tests/test_config.py::TestConfigBasic::test_config_file_corruption_handling", "tests/test_config.py::TestConfigBasic::test_config_partial_file", "tests/test_config.py::TestConfigCoverage::test_config_defaults", "tests/test_config.py::TestConfigCoverage::test_config_environment_variables", "tests/test_config.py::TestConfigCoverage::test_config_file_permissions", "tests/test_config.py::TestConfigCoverage::test_config_manager_singleton", "tests/test_config.py::TestConfigCoverage::test_config_nested_directories", "tests/test_config.py::TestConfigCoverage::test_config_unicode_handling", "tests/test_config.py::TestConfigCoverage::test_config_validation", "tests/test_config.py::TestConfigCoverage::test_setup_logging_levels", "tests/test_config.py::TestConfigEdgeCases::test_config_path_expansion_edge_cases", "tests/test_config.py::TestConfigEdgeCases::test_config_with_extra_fields", "tests/test_config.py::TestConfigEdgeCases::test_config_with_invalid_types", "tests/test_config.py::TestConfigEdgeCases::test_save_config_edge_cases", "tests/test_config.py::TestConfigEdgeCases::test_setup_logging_edge_cases", "tests/test_config.py::TestConfigFileOperations::test_load_config_from_file", "tests/test_config.py::TestConfigFileOperations::test_load_config_nonexistent_file", "tests/test_config.py::TestConfigFileOperations::test_save_config", "tests/test_config.py::TestConfigFunctions::test_get_config", "tests/test_config.py::TestConfigFunctions::test_get_config_manager", "tests/test_config.py::TestConfigFunctions::test_reset_config", "tests/test_config.py::TestConfigFunctions::test_setup_logging", "tests/test_config.py::TestConfigIntegration::test_config_file_corruption_handling", "tests/test_config.py::TestConfigIntegration::test_config_partial_file", "tests/test_config.py::TestConfigIntegration::test_config_precedence", "tests/test_config.py::TestSRSConfig::test_config_attributes", "tests/test_config.py::TestSRSConfig::test_config_initialization_custom", "tests/test_config.py::TestSRSConfig::test_config_initialization_defaults", "tests/test_config.py::TestSRSConfig::test_config_path_expansion", "tests/test_config.py::TestSRSConfig::test_config_string_representation", "tests/test_config.py::TestSRSConfig::test_config_validation_basic", "tests/test_csv_export.py::TestCSVExporter::test_csv_exporter_initialization", "tests/test_csv_export.py::TestCSVExporter::test_export_all_decks_combined", "tests/test_csv_export.py::TestCSVExporter::test_export_deck_basic", "tests/test_csv_export.py::TestCSVExporter::test_export_multiple_decks", "tests/test_csv_export.py::TestCSVExporter::test_export_nonexistent_deck", "tests/test_csv_export.py::TestCSVExporter::test_export_with_custom_delimiter", "tests/test_csv_export.py::TestCSVExporter::test_export_with_metadata", "tests/test_csv_export.py::TestCSVExporter::test_export_with_stats", "tests/test_csv_export.py::TestCSVExporter::test_full_export_with_all_data", "tests/test_csv_export.py::TestCSVExporter::test_get_supported_formats", "tests/test_csv_export.py::TestCSVExporter::test_preview_export", "tests/test_csv_import.py::TestCSVImporter::test_actual_import", "tests/test_csv_import.py::TestCSVImporter::test_add_tags_to_card_placeholder", "tests/test_csv_import.py::TestCSVImporter::test_create_dry_run_report", "tests/test_csv_import.py::TestCSVImporter::test_csv_importer_initialization", "tests/test_csv_import.py::TestCSVImporter::test_detect_delimiter_comma", "tests/test_csv_import.py::TestCSVImporter::test_detect_delimiter_fallback", "tests/test_csv_import.py::TestCSVImporter::test_detect_delimiter_tab", "tests/test_csv_import.py::TestCSVImporter::test_dry_run_import", "tests/test_csv_import.py::TestCSVImporter::test_extract_card_data_basic", "tests/test_csv_import.py::TestCSVImporter::test_extract_card_data_empty_fields", "tests/test_csv_import.py::TestCSVImporter::test_find_column_by_name", "tests/test_csv_import.py::TestCSVImporter::test_get_supported_formats", "tests/test_csv_import.py::TestCSVImporter::test_import_cards_with_errors", "tests/test_csv_import.py::TestCSVImporter::test_import_error_handling", "tests/test_csv_import.py::TestCSVImporter::test_import_nonexistent_file", "tests/test_csv_import.py::TestCSVImporter::test_import_with_custom_delimiter", "tests/test_csv_import.py::TestCSVImporter::test_import_with_merge", "tests/test_csv_import.py::TestCSVImporter::test_import_without_merge_existing_deck", "tests/test_csv_import.py::TestCSVImporter::test_parse_csv_skip_insufficient_columns", "tests/test_csv_import.py::TestCSVImporter::test_parse_csv_with_custom_mapping", "tests/test_csv_import.py::TestCSVImporter::test_parse_csv_with_headers", "tests/test_csv_import.py::TestCSVImporter::test_parse_csv_with_tags", "tests/test_csv_import.py::TestCSVImporter::test_parse_csv_without_headers", "tests/test_csv_import.py::TestCSVImporter::test_preview_file", "tests/test_csv_import.py::TestCSVImporter::test_preview_file_no_headers", "tests/test_csv_import.py::TestCSVImporter::test_validate_file_invalid_extension", "tests/test_csv_import.py::TestCSVImporter::test_validate_file_no_delimiters", "tests/test_csv_import.py::TestCSVImporter::test_validate_file_valid_csv", "tests/test_database.py::TestDatabase::test_backup_restore_functionality", "tests/test_database.py::TestDatabase::test_concurrent_access_safety", "tests/test_database.py::TestDatabase::test_data_persistence_between_sessions", "tests/test_database.py::TestDatabase::test_database_corruption_handling", "tests/test_database.py::TestDatabase::test_database_creation_on_first_run", "tests/test_database.py::TestDatabase::test_foreign_key_constraints", "tests/test_database.py::TestDatabase::test_indexes_creation", "tests/test_database.py::TestDatabase::test_query_timeout", "tests/test_database.py::TestDatabase::test_row_factory_configuration", "tests/test_database.py::TestDatabase::test_schema_creation", "tests/test_database.py::TestDatabase::test_transaction_rollback", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_singleton", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_with_custom_path", "tests/test_database.py::TestDatabaseGlobalInstance::test_reset_database", "tests/test_database.py::TestDatabasePerformance::test_database_operation_performance", "tests/test_database.py::TestDatabasePerformance::test_large_dataset_handling", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_check_daily_limits_exhausted", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_check_daily_limits_no_reviews", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_check_daily_limits_with_reviews", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_deck_creation_with_custom_limits", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_deck_creation_with_default_limits", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_deck_save_method", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_get_daily_limits", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_get_limited_due_cards_exclude_new", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_get_limited_due_cards_no_limits", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_get_limited_due_cards_with_limits", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_get_settings_dict", "tests/test_deck_limits.py::TestDeckLimitsAndSettings::test_update_settings_method", "tests/test_deck_limits.py::TestDeckLimitsDatabaseMigration::test_deck_limit_columns_exist", "tests/test_deck_limits.py::TestDeckLimitsDatabaseMigration::test_migration_record_exists", "tests/test_deck_limits.py::TestDeckLimitsDatabaseMigration::test_schema_version_4", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_check_daily_limits_deck_without_id", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_concurrent_limit_checking", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_deck_loading_with_null_values", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_get_all_decks_includes_new_fields", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_get_limited_due_cards_deck_without_id", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_limit_enforcement_with_mixed_card_types", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_limits_with_timezone_considerations", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_negative_limits_converted_to_zero", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_save_deck_without_id_raises_error", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_update_settings_with_dict_settings", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_update_settings_with_string_settings", "tests/test_deck_limits.py::TestDeckLimitsEdgeCases::test_very_large_limits", "tests/test_deck_limits.py::TestReviewSessionLimitIntegration::test_review_session_no_cards_when_limits_exhausted", "tests/test_deck_limits.py::TestReviewSessionLimitIntegration::test_review_session_respects_limits", "tests/test_difficulty_tracking.py::TestDifficultyDatabaseMigration::test_difficulty_columns_exist", "tests/test_difficulty_tracking.py::TestDifficultyDatabaseMigration::test_difficulty_index_exists", "tests/test_difficulty_tracking.py::TestDifficultyDatabaseMigration::test_migration_record_exists", "tests/test_difficulty_tracking.py::TestDifficultyDatabaseMigration::test_schema_version_3", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_card_loading_with_null_difficulty_values", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_difficulty_adjustment_missing_attributes", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_difficulty_precision", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_difficulty_update_invalid_rating", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_difficulty_update_negative_duration", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_difficulty_update_zero_duration", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_extreme_difficulty_values", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_large_consecutive_counters", "tests/test_difficulty_tracking.py::TestDifficultyEdgeCases::test_performance_stats_zero_repetitions", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_card_creation_with_difficulty_defaults", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_card_loading_with_difficulty_fields", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_card_save_with_difficulty_fields", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_consecutive_counters", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_difficulty_bounds", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_difficulty_level_classification", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_difficulty_update_easy_rating", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_difficulty_update_hard_rating", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_difficulty_update_rating_progression", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_is_difficult_classification", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_performance_stats", "tests/test_difficulty_tracking.py::TestDifficultyTracking::test_review_duration_impact", "tests/test_difficulty_tracking.py::TestReviewSessionDifficultyIntegration::test_review_session_saves_difficulty_changes", "tests/test_difficulty_tracking.py::TestReviewSessionDifficultyIntegration::test_review_session_updates_difficulty", "tests/test_difficulty_tracking.py::TestSM2AlgorithmDifficultyIntegration::test_consecutive_performance_adjustment", "tests/test_difficulty_tracking.py::TestSM2AlgorithmDifficultyIntegration::test_difficulty_adjustment_easy_card", "tests/test_difficulty_tracking.py::TestSM2AlgorithmDifficultyIntegration::test_difficulty_adjustment_hard_card", "tests/test_difficulty_tracking.py::TestSM2AlgorithmDifficultyIntegration::test_review_duration_adjustment", "tests/test_graduation.py::TestGraduationEngine::test_graduation_engine_initialization", "tests/test_graduation.py::TestGraduationEngine::test_learning_response_enum", "tests/test_graduation.py::TestLearningButtons::test_learning_buttons_for_graduated_card", "tests/test_graduation.py::TestLearningButtons::test_learning_buttons_for_new_card", "tests/test_graduation.py::TestLearningProgress::test_learning_progress_graduated_card", "tests/test_graduation.py::TestLearningProgress::test_learning_progress_learning_card", "tests/test_graduation.py::TestLearningProgress::test_learning_progress_new_card", "tests/test_graduation.py::TestLearningProgression::test_learning_card_again_response", "tests/test_graduation.py::TestLearningProgression::test_learning_card_easy_response", "tests/test_graduation.py::TestLearningProgression::test_learning_card_good_response", "tests/test_graduation.py::TestLearningProgression::test_learning_card_hard_response", "tests/test_graduation.py::TestLearningProgression::test_new_card_good_response", "tests/test_graduation.py::TestLearningStatistics::test_learning_statistics", "tests/test_graduation.py::TestRelearning::test_graduated_card_failure", "tests/test_graduation.py::TestRelearning::test_relearning_progression", "tests/test_init.py::TestInitialization::test_initialize_srs_creates_directories", "tests/test_init.py::TestInitialization::test_initialize_srs_default", "tests/test_init.py::TestInitialization::test_initialize_srs_precedence", "tests/test_init.py::TestInitialization::test_initialize_srs_with_config_file", "tests/test_init.py::TestInitialization::test_initialize_srs_with_environment_variables", "tests/test_init.py::TestInitialization::test_initialize_srs_with_invalid_config", "tests/test_init.py::TestInitialization::test_initialize_srs_with_nonexistent_config", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_corrupted_database", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_disk_full", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_permission_error", "tests/test_init.py::TestLoggingSetup::test_setup_logging_creates_log_directory", "tests/test_init.py::TestLoggingSetup::test_setup_logging_debug_level", "tests/test_init.py::TestLoggingSetup::test_setup_logging_default", "tests/test_init.py::TestLoggingSetup::test_setup_logging_with_file", "tests/test_init.py::TestPackageImports::test_circular_imports", "tests/test_init.py::TestPackageImports::test_main_imports", "tests/test_init.py::TestSystemIntegration::test_full_initialization_workflow", "tests/test_init.py::TestSystemIntegration::test_initialization_thread_safety", "tests/test_init.py::TestSystemIntegration::test_multiple_initialization_calls", "tests/test_init.py::TestVersionInfo::test_version_exists", "tests/test_init.py::TestVersionInfo::test_version_format", "tests/test_integration.py::TestEndToEndWorkflows::test_complete_deck_lifecycle", "tests/test_integration.py::TestEndToEndWorkflows::test_csv_import_workflow", "tests/test_integration.py::TestEndToEndWorkflows::test_error_handling_workflow", "tests/test_integration.py::TestEndToEndWorkflows::test_review_session_workflow", "tests/test_integration.py::TestErrorRecovery::test_database_error_recovery", "tests/test_integration.py::TestErrorRecovery::test_file_system_error_recovery", "tests/test_integration.py::TestPerformanceIntegration::test_concurrent_operations", "tests/test_integration.py::TestPerformanceIntegration::test_large_deck_performance", "tests/test_integration.py::TestSystemIntegration::test_component_interaction", "tests/test_integration.py::TestSystemIntegration::test_database_integration", "tests/test_integration.py::TestSystemIntegration::test_initialization_integration", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_escape_markdown", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_export_deck_basic", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_export_nonexistent_deck", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_export_with_stats", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_export_without_metadata", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_get_supported_formats", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_markdown_exporter_initialization", "tests/test_markdown_import_export.py::TestMarkdownExporter::test_preview_export", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_actual_import", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_dry_run_import", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_extract_cards_basic", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_extract_deck_info", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_get_supported_formats", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_import_with_custom_deck_name", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_markdown_importer_initialization", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_validate_file_invalid_extension", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_validate_file_invalid_markdown", "tests/test_markdown_import_export.py::TestMarkdownImporter::test_validate_file_valid_markdown", "tests/test_models.py::TestCard::test_card_content_trimming", "tests/test_models.py::TestCard::test_card_creation_with_invalid_data", "tests/test_models.py::TestCard::test_card_creation_with_valid_data", "tests/test_models.py::TestCard::test_card_save_functionality", "tests/test_models.py::TestCard::test_get_all_due_cards", "tests/test_models.py::TestCard::test_get_card_by_id", "tests/test_models.py::TestCard::test_get_cards_by_deck", "tests/test_models.py::TestCard::test_get_due_cards", "tests/test_models.py::TestCard::test_unicode_support_in_card_content", "tests/test_models.py::TestDeck::test_deck_card_counts", "tests/test_models.py::TestDeck::test_deck_creation_with_invalid_names", "tests/test_models.py::TestDeck::test_deck_creation_with_valid_name", "tests/test_models.py::TestDeck::test_deck_deletion", "tests/test_models.py::TestDeck::test_deck_deletion_cascades_to_cards", "tests/test_models.py::TestDeck::test_deck_name_trimming", "tests/test_models.py::TestDeck::test_duplicate_deck_handling", "tests/test_models.py::TestDeck::test_get_all_decks", "tests/test_models.py::TestDeck::test_get_deck_by_id", "tests/test_models.py::TestDeck::test_get_deck_by_name", "tests/test_models.py::TestDeck::test_get_nonexistent_deck", "tests/test_models.py::TestModelRelationships::test_card_review_relationship", "tests/test_models.py::TestModelRelationships::test_cascading_deletes", "tests/test_models.py::TestModelRelationships::test_deck_card_relationship", "tests/test_models.py::TestReview::test_get_reviews_by_card", "tests/test_models.py::TestReview::test_review_creation_with_all_valid_ratings", "tests/test_models.py::TestReview::test_review_creation_with_invalid_rating", "tests/test_models.py::TestReview::test_review_creation_with_valid_data", "tests/test_models.py::TestReview::test_review_history_tracking", "tests/test_progress.py::TestAchievementSystem::test_create_achievement", "tests/test_progress.py::TestAchievementSystem::test_prevent_duplicate_unlock", "tests/test_progress.py::TestAchievementSystem::test_review_based_achievements", "tests/test_progress.py::TestAchievementSystem::test_session_based_achievements", "tests/test_progress.py::TestAchievementSystem::test_streak_achievements", "tests/test_progress.py::TestAchievementSystem::test_unlock_achievement", "tests/test_progress.py::TestProgressDatabaseMigration::test_progress_indexes_created", "tests/test_progress.py::TestProgressDatabaseMigration::test_progress_tables_exist", "tests/test_progress.py::TestProgressDatabaseMigration::test_schema_version_6", "tests/test_progress.py::TestProgressSummary::test_progress_summary_empty", "tests/test_progress.py::TestProgressSummary::test_progress_summary_with_activity", "tests/test_progress.py::TestProgressTracker::test_achievement_creation", "tests/test_progress.py::TestProgressTracker::test_progress_tracker_initialization", "tests/test_progress.py::TestProgressTracker::test_user_streak_creation", "tests/test_progress.py::TestStreakTracking::test_consecutive_day_streak", "tests/test_progress.py::TestStreakTracking::test_create_new_streak", "tests/test_progress.py::TestStreakTracking::test_longest_streak_tracking", "tests/test_progress.py::TestStreakTracking::test_same_day_streak_no_change", "tests/test_progress.py::TestStreakTracking::test_streak_reset_after_gap", "tests/test_project_structure.py::TestConfiguration::test_config_manager", "tests/test_project_structure.py::TestConfiguration::test_default_config", "tests/test_project_structure.py::TestLogging::test_logging_setup", "tests/test_project_structure.py::TestProjectStructure::test_algorithm_module_import", "tests/test_project_structure.py::TestProjectStructure::test_cli_module_import", "tests/test_project_structure.py::TestProjectStructure::test_config_module_import", "tests/test_project_structure.py::TestProjectStructure::test_database_module_import", "tests/test_project_structure.py::TestProjectStructure::test_main_imports", "tests/test_project_structure.py::TestProjectStructure::test_models_module_import", "tests/test_project_structure.py::TestProjectStructure::test_review_module_import", "tests/test_project_structure.py::TestProjectStructure::test_srs_package_import", "tests/test_project_structure.py::TestProjectStructure::test_utils_module_import", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_nonexistent_deck", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_success", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_with_specific_cards", "tests/test_review.py::TestReviewSession::test_card_ordering_by_priority", "tests/test_review.py::TestReviewSession::test_end_session", "tests/test_review.py::TestReviewSession::test_progress_tracking", "tests/test_review.py::TestReviewSession::test_review_current_card_no_cards", "tests/test_review.py::TestReviewSession::test_review_current_card_success", "tests/test_review.py::TestReviewSession::test_session_completion", "tests/test_review.py::TestReviewSession::test_session_creation_with_due_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_no_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_specific_cards", "tests/test_review.py::TestReviewSession::test_session_interruption_and_resumption", "tests/test_review.py::TestReviewSession::test_session_state_restoration", "tests/test_review.py::TestReviewSession::test_session_state_saving", "tests/test_review.py::TestReviewSession::test_session_summary", "tests/test_review.py::TestReviewSession::test_skip_current_card", "tests/test_review.py::TestReviewSession::test_skip_current_card_no_cards", "tests/test_review.py::TestReviewSessionPerformance::test_large_session_creation_performance", "tests/test_review.py::TestReviewSessionPerformance::test_review_session_memory_usage", "tests/test_review.py::TestSessionStats::test_completion_percentage_calculation", "tests/test_review.py::TestSessionStats::test_duration_calculation", "tests/test_review.py::TestSessionStats::test_session_stats_initialization", "tests/test_review_filters.py::TestReviewFilters::test_combined_filters", "tests/test_review_filters.py::TestReviewFilters::test_create_easy_cards_session", "tests/test_review_filters.py::TestReviewFilters::test_create_filtered_review_session", "tests/test_review_filters.py::TestReviewFilters::test_create_hard_cards_session", "tests/test_review_filters.py::TestReviewFilters::test_create_learning_cards_session", "tests/test_review_filters.py::TestReviewFilters::test_create_new_cards_session", "tests/test_review_filters.py::TestReviewFilters::test_create_overdue_cards_session", "tests/test_review_filters.py::TestReviewFilters::test_difficulty_filter_max", "tests/test_review_filters.py::TestReviewFilters::test_difficulty_filter_min", "tests/test_review_filters.py::TestReviewFilters::test_difficulty_filter_range", "tests/test_review_filters.py::TestReviewFilters::test_due_only_filter", "tests/test_review_filters.py::TestReviewFilters::test_filter_to_search_query", "tests/test_review_filters.py::TestReviewFilters::test_get_card_type", "tests/test_review_filters.py::TestReviewFilters::test_interval_filter", "tests/test_review_filters.py::TestReviewFilters::test_is_card_due", "tests/test_review_filters.py::TestReviewFilters::test_is_card_overdue", "tests/test_review_filters.py::TestReviewFilters::test_learning_state_filter", "tests/test_review_filters.py::TestReviewFilters::test_no_matching_cards_filter", "tests/test_review_filters.py::TestReviewFilters::test_nonexistent_deck_filter", "tests/test_review_filters.py::TestReviewFilters::test_overdue_only_filter", "tests/test_review_filters.py::TestReviewFilters::test_repetitions_filter", "tests/test_review_filters.py::TestReviewFilters::test_review_filter_initialization", "tests/test_search.py::TestSearchEngine::test_and_operator_search", "tests/test_search.py::TestSearchEngine::test_back_field_search", "tests/test_search.py::TestSearchEngine::test_basic_text_search", "tests/test_search.py::TestSearchEngine::test_case_insensitive_search", "tests/test_search.py::TestSearchEngine::test_combined_filters_search", "tests/test_search.py::TestSearchEngine::test_complex_search_query", "tests/test_search.py::TestSearchEngine::test_deck_filtered_search", "tests/test_search.py::TestSearchEngine::test_difficulty_filter_search", "tests/test_search.py::TestSearchEngine::test_empty_search_query", "tests/test_search.py::TestSearchEngine::test_front_field_search", "tests/test_search.py::TestSearchEngine::test_interval_filter_search", "tests/test_search.py::TestSearchEngine::test_learning_state_filter_search", "tests/test_search.py::TestSearchEngine::test_no_results_search", "tests/test_search.py::TestSearchEngine::test_or_operator_search", "tests/test_search.py::TestSearchEngine::test_query_parsing", "tests/test_search.py::TestSearchEngine::test_quoted_phrase_search", "tests/test_search.py::TestSearchEngine::test_search_engine_initialization", "tests/test_search.py::TestSearchEngine::test_search_pagination", "tests/test_search.py::TestSearchEngine::test_search_result_ranking", "tests/test_search.py::TestSearchEngine::test_search_suggestions", "tests/test_search.py::TestSearchEngine::test_search_with_matches", "tests/test_smart_scheduling.py::TestCardPrioritization::test_score_due_today_cards", "tests/test_smart_scheduling.py::TestCardPrioritization::test_score_new_cards", "tests/test_smart_scheduling.py::TestCardPrioritization::test_score_overdue_cards", "tests/test_smart_scheduling.py::TestCardPrioritization::test_sort_cards_by_priority", "tests/test_smart_scheduling.py::TestSessionLimits::test_apply_max_cards_limit", "tests/test_smart_scheduling.py::TestSessionLimits::test_apply_new_cards_limit", "tests/test_smart_scheduling.py::TestSessionLimits::test_apply_review_cards_limit", "tests/test_smart_scheduling.py::TestSmartScheduler::test_card_score_creation", "tests/test_smart_scheduling.py::TestSmartScheduler::test_scheduler_initialization", "tests/test_smart_scheduling.py::TestSmartScheduler::test_session_limits_creation", "tests/test_smart_scheduling.py::TestSmartSession::test_create_due_cards_only_session", "tests/test_smart_scheduling.py::TestSmartSession::test_create_new_cards_only_session", "tests/test_smart_scheduling.py::TestSmartSession::test_create_smart_session", "tests/test_smart_scheduling.py::TestTimeBoxedSessions::test_create_time_boxed_session", "tests/test_smart_scheduling.py::TestTimeBoxedSessions::test_estimate_session_time", "tests/test_smart_scheduling.py::TestTimeBoxedSessions::test_get_session_statistics", "tests/test_statistics.py::TestPerformanceSummary::test_calculate_study_streak", "tests/test_statistics.py::TestPerformanceSummary::test_get_performance_summary_empty", "tests/test_statistics.py::TestPerformanceSummary::test_get_performance_summary_with_data", "tests/test_statistics.py::TestRetentionRateCalculation::test_calculate_retention_rate_basic", "tests/test_statistics.py::TestRetentionRateCalculation::test_calculate_retention_rate_deck_specific", "tests/test_statistics.py::TestRetentionRateCalculation::test_calculate_retention_rate_no_reviews", "tests/test_statistics.py::TestStatisticsDatabaseMigration::test_reviews_table_enhanced", "tests/test_statistics.py::TestStatisticsDatabaseMigration::test_schema_version_5", "tests/test_statistics.py::TestStatisticsDatabaseMigration::test_statistics_indexes_created", "tests/test_statistics.py::TestStatisticsDatabaseMigration::test_statistics_tables_exist", "tests/test_statistics.py::TestStatisticsDatabaseOperations::test_get_daily_stats", "tests/test_statistics.py::TestStatisticsDatabaseOperations::test_get_deck_stats", "tests/test_statistics.py::TestStatisticsDatabaseOperations::test_record_daily_stats", "tests/test_statistics.py::TestStatisticsDatabaseOperations::test_record_deck_stats", "tests/test_statistics.py::TestStatisticsEdgeCases::test_calculate_average_ease_factor_no_data", "tests/test_statistics.py::TestStatisticsEdgeCases::test_calculate_average_ease_factor_with_data", "tests/test_statistics.py::TestStatisticsEdgeCases::test_daily_stats_replace_existing", "tests/test_statistics.py::TestStatisticsEdgeCases::test_deck_stats_replace_existing", "tests/test_statistics.py::TestStatisticsEngine::test_daily_stats_creation", "tests/test_statistics.py::TestStatisticsEngine::test_deck_stats_creation", "tests/test_statistics.py::TestStatisticsEngine::test_statistics_engine_initialization", "tests/test_statistics.py::TestStatisticsUpdates::test_update_daily_statistics", "tests/test_statistics.py::TestStatisticsUpdates::test_update_deck_statistics", "tests/test_stats_commands.py::TestStatsCommand::test_stats_command_basic", "tests/test_stats_commands.py::TestStatsCommand::test_stats_command_different_periods", "tests/test_stats_commands.py::TestStatsCommand::test_stats_command_nonexistent_deck", "tests/test_stats_commands.py::TestStatsCommand::test_stats_command_specific_deck", "tests/test_stats_commands.py::TestStatsCommand::test_stats_forecast", "tests/test_stats_commands.py::TestStatsCommand::test_stats_performance_trends", "tests/test_stats_commands.py::TestStatsCommand::test_stats_retention_analysis", "tests/test_stats_commands.py::TestStatsExport::test_stats_export_auto_filename", "tests/test_stats_commands.py::TestStatsExport::test_stats_export_csv", "tests/test_stats_commands.py::TestStatsExport::test_stats_export_json", "tests/test_stats_commands.py::TestVisualizationFunctions::test_achievement_display", "tests/test_stats_commands.py::TestVisualizationFunctions::test_export_data_to_csv", "tests/test_stats_commands.py::TestVisualizationFunctions::test_horizontal_bar_chart", "tests/test_stats_commands.py::TestVisualizationFunctions::test_line_chart_creation", "tests/test_stats_commands.py::TestVisualizationFunctions::test_progress_bar_creation", "tests/test_stats_commands.py::TestVisualizationFunctions::test_summary_box_creation", "tests/test_tags.py::TestCardTagRelationships::test_add_tag_by_name_to_card", "tests/test_tags.py::TestCardTagRelationships::test_add_tag_to_card", "tests/test_tags.py::TestCardTagRelationships::test_card_tag_database_operations", "tests/test_tags.py::TestCardTagRelationships::test_get_cards_by_tag", "tests/test_tags.py::TestCardTagRelationships::test_has_tag_functionality", "tests/test_tags.py::TestCardTagRelationships::test_multiple_tags_per_card", "tests/test_tags.py::TestCardTagRelationships::test_remove_tag_from_card", "tests/test_tags.py::TestCardTagRelationships::test_tag_deletion_removes_associations", "tests/test_tags.py::TestCardTagRelationships::test_tag_usage_stats", "tests/test_tags.py::TestTagDatabaseOperations::test_card_tag_database_operations", "tests/test_tags.py::TestTagDatabaseOperations::test_create_tag_database", "tests/test_tags.py::TestTagDatabaseOperations::test_delete_tag_database", "tests/test_tags.py::TestTagDatabaseOperations::test_get_all_tags_database", "tests/test_tags.py::TestTagDatabaseOperations::test_get_tag_by_name_database", "tests/test_tags.py::TestTagDatabaseOperations::test_tag_usage_stats_database", "tests/test_tags.py::TestTagDatabaseOperations::test_update_tag_database", "tests/test_tags.py::TestTagEdgeCases::test_add_nonexistent_tag_by_name", "tests/test_tags.py::TestTagEdgeCases::test_card_tag_operations_without_ids", "tests/test_tags.py::TestTagEdgeCases::test_database_constraint_violations", "tests/test_tags.py::TestTagEdgeCases::test_remove_nonexistent_tag_by_name", "tests/test_tags.py::TestTagEdgeCases::test_tag_operations_without_id", "tests/test_tags.py::TestTagEdgeCases::test_tag_post_init_validation", "tests/test_tags.py::TestTagEdgeCases::test_tag_update_no_changes", "tests/test_tags.py::TestTagEdgeCases::test_tag_with_invalid_color", "tests/test_tags.py::TestTagMigration::test_card_tags_table_exists", "tests/test_tags.py::TestTagMigration::test_card_tags_table_structure", "tests/test_tags.py::TestTagMigration::test_foreign_key_constraints", "tests/test_tags.py::TestTagMigration::test_indexes_created", "tests/test_tags.py::TestTagMigration::test_schema_version_tracking", "tests/test_tags.py::TestTagMigration::test_tags_table_exists", "tests/test_tags.py::TestTagMigration::test_tags_table_structure", "tests/test_tags.py::TestTagModel::test_tag_creation_basic", "tests/test_tags.py::TestTagModel::test_tag_creation_validation", "tests/test_tags.py::TestTagModel::test_tag_creation_with_color", "tests/test_tags.py::TestTagModel::test_tag_delete", "tests/test_tags.py::TestTagModel::test_tag_delete_without_id", "tests/test_tags.py::TestTagModel::test_tag_duplicate_name", "tests/test_tags.py::TestTagModel::test_tag_get_all", "tests/test_tags.py::TestTagModel::test_tag_get_by_id", "tests/test_tags.py::TestTagModel::test_tag_get_by_id_not_found", "tests/test_tags.py::TestTagModel::test_tag_get_by_name", "tests/test_tags.py::TestTagModel::test_tag_get_by_name_not_found", "tests/test_tags.py::TestTagModel::test_tag_name_normalization", "tests/test_tags.py::TestTagModel::test_tag_update", "tests/test_tags.py::TestTagModel::test_tag_update_validation", "tests/test_tags.py::TestTagModel::test_tag_update_without_id", "tests/test_utils.py::TestAdditionalUtilityFunctions::test_format_functions_edge_cases", "tests/test_utils.py::TestAdditionalUtilityFunctions::test_sanitize_filename_edge_cases", "tests/test_utils.py::TestAdditionalUtilityFunctions::test_truncate_text_edge_cases", "tests/test_utils.py::TestAdditionalUtilityFunctions::test_validate_card_content_edge_cases", "tests/test_utils.py::TestAdditionalUtilityFunctions::test_validate_deck_name_edge_cases", "tests/test_utils.py::TestFileOperations::test_ensure_directory", "tests/test_utils.py::TestFileOperations::test_ensure_directory_basic", "tests/test_utils.py::TestFileOperations::test_get_file_size_mb", "tests/test_utils.py::TestFileOperations::test_get_file_size_mb_basic", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_basic", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_invalid", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_nonexistent", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_valid", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_delimiter", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_quotes", "tests/test_utils.py::TestFileOperations::test_sanitize_filename", "tests/test_utils.py::TestFileOperations::test_sanitize_filename_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_card_counts", "tests/test_utils.py::TestFormattingFunctions::test_format_card_counts_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_datetime", "tests/test_utils.py::TestFormattingFunctions::test_format_datetime_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_duration", "tests/test_utils.py::TestFormattingFunctions::test_format_duration_basic", "tests/test_utils.py::TestFormattingFunctions::test_truncate_text", "tests/test_utils.py::TestFormattingFunctions::test_truncate_text_basic", "tests/test_utils.py::TestTextProcessing::test_clean_text", "tests/test_utils.py::TestUtilsCoverage::test_clean_text_comprehensive", "tests/test_utils.py::TestUtilsCoverage::test_ensure_directory_edge_cases", "tests/test_utils.py::TestUtilsCoverage::test_export_cards_to_csv", "tests/test_utils.py::TestUtilsCoverage::test_format_card_counts_edge_cases", "tests/test_utils.py::TestUtilsCoverage::test_format_datetime_edge_cases", "tests/test_utils.py::TestUtilsCoverage::test_get_file_size_mb_edge_cases", "tests/test_utils.py::TestUtilsCoverage::test_parse_csv_file_edge_cases", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_basic", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_edge_cases", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_invalid", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_valid", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_basic", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_invalid", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_valid"]