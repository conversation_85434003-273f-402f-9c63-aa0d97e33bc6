# TODO3.md - Phase 2: Web Interface & Supabase Migration Implementation Plan

## Overview

This document provides a comprehensive implementation roadmap for **Phase 2: Web Interface & Supabase Migration** of the spaced repetition learning system. Phase 2 represents a critical architectural transition from local SQLite to Supabase, enabling multi-user support, real-time features, and robust authentication while maintaining backwards compatibility.

## Phase 2 Requirements Summary

Based on the PRD document, Phase 2 introduces:

1. **Supabase Migration & Dual-Mode Architecture**
2. **Authentication & User Management**
3. **React-based Web Interface**
4. **Real-time Sync & Collaboration**
5. **Visual Analytics Dashboard**
6. **Migration Tools & Data Portability**

## Implementation Timeline

**Estimated Duration:** 8-10 weeks
- **Weeks 1-2:** Supabase setup, schema migration, dual-mode architecture
- **Weeks 3-4:** Authentication implementation, RLS policies
- **Weeks 5-6:** React web interface development
- **Weeks 7-8:** Real-time sync, Edge functions
- **Weeks 9-10:** Migration tools, dual-mode support, testing

---

## Phase 2.1: Supabase Foundation & Dual-Mode Architecture (Weeks 1-2)

### Task 2.1.1: Supabase Project Setup & Environment Configuration
**Priority:** Critical | **Estimated Time:** 8 hours | **Dependencies:** None

#### Description
Set up Supabase project, configure environments, and establish connection infrastructure.

#### Technical Specifications
- Create Supabase project with proper organization
- Configure development, staging, and production environments
- Set up environment variables and configuration management
- Establish secure connection patterns

#### Implementation Details
- **File:** `.env.example` (new)
  - Document all required environment variables
  - Provide example configuration
  - Include security guidelines

- **File:** `srs/config.py`
  - Add Supabase configuration management
  - Implement environment detection
  - Add connection validation

- **File:** `srs/supabase_client.py` (new)
  - Create Supabase client wrapper
  - Implement connection pooling
  - Add error handling and retry logic

#### Acceptance Criteria
- [ ] Supabase project created and configured
- [ ] Environment variables properly managed
- [ ] Connection to Supabase established
- [ ] Configuration validation implemented
- [ ] 95%+ test coverage for configuration

#### Test Requirements
- Unit tests for configuration management
- Integration tests for Supabase connection
- Environment validation tests
- Error handling tests

### Task 2.1.2: Supabase Schema Migration & RLS Setup
**Priority:** Critical | **Estimated Time:** 16 hours | **Dependencies:** Task 2.1.1

#### Description
Create Supabase database schema with Row Level Security policies and migrate from SQLite structure.

#### Technical Specifications
```sql
-- Core schema with UUID primary keys and RLS
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  username TEXT UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (id)
);

CREATE TABLE decks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT DEFAULT '',
  daily_new_limit INTEGER DEFAULT 20,
  daily_review_limit INTEGER DEFAULT 200,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE cards (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  deck_id UUID REFERENCES decks(id) ON DELETE CASCADE,
  front TEXT NOT NULL,
  back TEXT NOT NULL,
  interval INTEGER DEFAULT 0,
  repetitions INTEGER DEFAULT 0,
  ease_factor REAL DEFAULT 2.5,
  due_date TIMESTAMPTZ DEFAULT NOW(),
  difficulty REAL DEFAULT 0.5,
  learning_state TEXT DEFAULT 'new',
  current_step INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE decks ENABLE ROW LEVEL SECURITY;
ALTER TABLE cards ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can only see own decks" ON decks
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only see own cards" ON cards
  FOR ALL USING (
    deck_id IN (SELECT id FROM decks WHERE user_id = auth.uid())
  );
```

#### Implementation Details
- **File:** `supabase/migrations/001_initial_schema.sql` (new)
  - Create complete schema with RLS
  - Add proper indexes for performance
  - Include triggers for updated_at timestamps

- **File:** `srs/supabase_models.py` (new)
  - Create Supabase-specific model classes
  - Implement UUID handling
  - Add RLS-aware query methods

#### Acceptance Criteria
- [ ] Complete schema created in Supabase
- [ ] RLS policies properly configured
- [ ] All indexes created for performance
- [ ] Schema validation tests pass
- [ ] 95%+ test coverage for schema operations

### Task 2.1.3: Dual-Mode Architecture Implementation
**Priority:** Critical | **Estimated Time:** 20 hours | **Dependencies:** Task 2.1.2

#### Description
Implement dual-mode architecture allowing seamless switching between local SQLite and cloud Supabase modes.

#### Technical Specifications
- Abstract database interface for mode switching
- Automatic mode detection based on configuration
- Graceful fallback mechanisms
- Command-line flags for explicit mode selection

#### Implementation Details
- **File:** `srs/database_interface.py` (new)
  - Create abstract database interface
  - Define common operations for both modes
  - Implement mode detection logic

- **File:** `srs/sqlite_adapter.py` (new)
  - Wrap existing SQLite functionality
  - Implement interface compliance
  - Add migration compatibility

- **File:** `srs/supabase_adapter.py` (new)
  - Implement Supabase database operations
  - Handle UUID conversions
  - Add real-time subscriptions

- **File:** `srs/cli.py`
  - Add --local and --cloud flags
  - Update command handlers for dual-mode
  - Add mode status indicators

#### Command Specifications
```bash
srs review --local          # Force local SQLite mode
srs review --cloud          # Force cloud Supabase mode
srs review                   # Auto-detect mode
srs status --mode            # Show current mode
srs migrate-to-cloud         # Migrate local data to cloud
```

#### Acceptance Criteria
- [ ] Dual-mode architecture implemented
- [ ] Automatic mode detection functional
- [ ] Command-line flags working
- [ ] Graceful fallback implemented
- [ ] 95%+ test coverage for mode switching

---

## Phase 2.2: Authentication & User Management (Weeks 3-4)

### Task 2.2.1: Supabase Authentication Integration
**Priority:** High | **Estimated Time:** 12 hours | **Dependencies:** Task 2.1.3

#### Description
Integrate Supabase authentication with email/password and OAuth providers.

#### Technical Specifications
- Email/password authentication
- Google OAuth integration
- Magic link authentication
- Session management and persistence

#### Implementation Details
- **File:** `srs/auth.py` (new)
  - Create authentication service
  - Implement login/logout flows
  - Add session management
  - Handle authentication errors

- **File:** `srs/cli.py`
  - Add authentication commands
  - Update commands to require auth in cloud mode
  - Add user session indicators

#### Command Specifications
```bash
srs login                    # Interactive login
srs login --email <EMAIL>  # Email login
srs logout                   # Logout current user
srs register                 # Create new account
srs profile                  # Show user profile
```

#### Acceptance Criteria
- [ ] Email/password authentication working
- [ ] OAuth providers integrated
- [ ] Session persistence implemented
- [ ] CLI authentication commands functional
- [ ] 95%+ test coverage for authentication

### Task 2.2.2: User Profile Management
**Priority:** Medium | **Estimated Time:** 8 hours | **Dependencies:** Task 2.2.1

#### Description
Implement user profile management with preferences and settings.

#### Implementation Details
- **File:** `srs/profile.py` (new)
  - Create profile management service
  - Implement preferences storage
  - Add profile validation

- **File:** `srs/cli.py`
  - Add profile management commands
  - Implement settings configuration

#### Acceptance Criteria
- [ ] User profiles created automatically
- [ ] Profile preferences manageable
- [ ] Settings persistence working
- [ ] Profile validation implemented
- [ ] 95%+ test coverage for profile management

---

## Phase 2.3: React Web Interface (Weeks 5-6)

### Task 2.3.1: React Application Setup & Architecture
**Priority:** High | **Estimated Time:** 16 hours | **Dependencies:** Task 2.2.2

#### Description
Set up React application with modern tooling and component architecture.

#### Technical Specifications
- Vite for build tooling
- TypeScript for type safety
- Tailwind CSS for styling
- React Router for navigation
- Zustand for state management

#### Implementation Details
- **Directory:** `web/` (new)
  - Set up React application structure
  - Configure build and development tools
  - Implement component library

- **File:** `web/src/lib/supabase.ts` (new)
  - Configure Supabase client for web
  - Implement authentication hooks
  - Add real-time subscriptions

#### Acceptance Criteria
- [ ] React application properly configured
- [ ] TypeScript setup and working
- [ ] Component library established
- [ ] Supabase integration functional
- [ ] Development environment ready

### Task 2.3.2: Authentication UI Components
**Priority:** High | **Estimated Time:** 12 hours | **Dependencies:** Task 2.3.1

#### Description
Create authentication UI components for login, registration, and profile management.

#### Implementation Details
- **File:** `web/src/components/auth/` (new)
  - LoginForm component
  - RegisterForm component
  - ProfileSettings component
  - AuthGuard component

#### Acceptance Criteria
- [ ] Login/register forms functional
- [ ] OAuth integration working
- [ ] Profile management UI complete
- [ ] Authentication state management working
- [ ] Responsive design implemented

### Task 2.3.3: Review Interface Components
**Priority:** High | **Estimated Time:** 20 hours | **Dependencies:** Task 2.3.2

#### Description
Create modern review interface with keyboard shortcuts and smooth animations.

#### Technical Specifications
- Card flip animations
- Keyboard shortcuts (spacebar, 1-4 keys)
- Progress indicators
- Session statistics
- Responsive design

#### Implementation Details
- **File:** `web/src/components/review/` (new)
  - ReviewSession component
  - CardDisplay component
  - RatingButtons component
  - ProgressIndicator component

#### Acceptance Criteria
- [ ] Review interface fully functional
- [ ] Keyboard shortcuts working
- [ ] Animations smooth and responsive
- [ ] Progress tracking accurate
- [ ] Mobile-friendly design

---

## Phase 2.4: Real-time Sync & Edge Functions (Weeks 7-8)

### Task 2.4.1: Real-time Synchronization
**Priority:** High | **Estimated Time:** 16 hours | **Dependencies:** Task 2.3.3

#### Description
Implement real-time synchronization between web interface and database using Supabase Realtime.

#### Technical Specifications
- Live updates across browser tabs
- Conflict-free review merging
- Offline queue with background sync
- Real-time progress updates

#### Implementation Details
- **File:** `web/src/hooks/useRealtime.ts` (new)
  - Implement real-time subscriptions
  - Handle connection state
  - Add conflict resolution

- **File:** `srs/sync.py` (new)
  - Create sync service for CLI
  - Implement offline queue
  - Add conflict resolution logic

#### Acceptance Criteria
- [ ] Real-time updates working
- [ ] Conflict resolution implemented
- [ ] Offline functionality working
- [ ] Sync status indicators functional
- [ ] 95%+ test coverage for sync logic

### Task 2.4.2: Edge Functions for Complex Operations
**Priority:** Medium | **Estimated Time:** 12 hours | **Dependencies:** Task 2.4.1

#### Description
Implement Supabase Edge Functions for server-side algorithm processing and complex operations.

#### Technical Specifications
```typescript
// supabase/functions/process-review/index.ts
serve(async (req) => {
  const { cardId, rating } = await req.json()
  
  // Apply SM-2 algorithm
  // Update card with new interval
  // Log review for analytics
  // Return next card
})
```

#### Implementation Details
- **File:** `supabase/functions/process-review/index.ts` (new)
  - Implement SM-2 algorithm processing
  - Handle review logging
  - Return optimized next card

- **File:** `supabase/functions/sync-reviews/index.ts` (new)
  - Handle conflict resolution
  - Merge review data
  - Return unified state

#### Acceptance Criteria
- [ ] Edge functions deployed and working
- [ ] Algorithm processing accurate
- [ ] Performance optimized
- [ ] Error handling robust
- [ ] 95%+ test coverage for functions

---

## Phase 2.5: Migration Tools & Data Portability (Weeks 9-10)

### Task 2.5.1: Data Migration CLI Tool
**Priority:** High | **Estimated Time:** 16 hours | **Dependencies:** Task 2.4.2

#### Description
Create comprehensive migration tool for moving data from SQLite to Supabase.

#### Implementation Details
- **File:** `srs/migration.py` (new)
  - Create migration service
  - Implement data validation
  - Add progress reporting
  - Handle error recovery

#### Command Specifications
```bash
srs migrate-to-cloud                 # Interactive migration
srs migrate-to-cloud --dry-run       # Preview migration
srs migrate-to-cloud --force         # Force migration
srs backup --local                   # Create local backup
srs sync                             # Sync local changes to cloud
```

#### Acceptance Criteria
- [ ] Migration tool functional
- [ ] Data validation implemented
- [ ] Progress reporting working
- [ ] Error recovery robust
- [ ] 95%+ test coverage for migration

### Task 2.5.2: Data Export & Backup Systems
**Priority:** Medium | **Estimated Time:** 12 hours | **Dependencies:** Task 2.5.1

#### Description
Implement comprehensive data export and backup systems for data portability.

#### Implementation Details
- **File:** `srs/backup.py` (new)
  - Create backup service
  - Implement multiple export formats
  - Add compression and encryption

#### Acceptance Criteria
- [ ] Backup system functional
- [ ] Multiple export formats supported
- [ ] Data integrity verified
- [ ] Restore functionality working
- [ ] 95%+ test coverage for backup

---

## Phase 2.6: Testing & Quality Assurance (Week 10)

### Task 2.6.1: Comprehensive Integration Testing
**Priority:** Critical | **Estimated Time:** 20 hours | **Dependencies:** All previous tasks

#### Description
Develop comprehensive test suite for Phase 2 features to achieve 95%+ test coverage.

#### Test Categories
- **Authentication Tests**
  - Login/logout flows
  - Session management
  - OAuth integration
  - Error handling

- **Dual-Mode Tests**
  - Mode switching
  - Data consistency
  - Fallback mechanisms
  - Performance comparison

- **Real-time Sync Tests**
  - Live updates
  - Conflict resolution
  - Offline functionality
  - Data integrity

- **Migration Tests**
  - Data migration accuracy
  - Error recovery
  - Performance benchmarks
  - Rollback functionality

#### Acceptance Criteria
- [ ] 95%+ test coverage achieved
- [ ] All integration tests passing
- [ ] Performance benchmarks met
- [ ] Security tests passing
- [ ] Documentation updated

---

## Quality Standards

### Code Quality
- **Test Coverage:** 95%+ for all new code
- **TypeScript:** Strict mode enabled for web components
- **Security:** All authentication flows secured
- **Performance:** <2s load times for web interface

### Database Standards
- **RLS Policies:** All tables properly secured
- **Indexes:** All queries optimized
- **Migrations:** All migrations reversible
- **Backup:** Automated backup systems

### User Experience Standards
- **Responsive Design:** Mobile-first approach
- **Accessibility:** WCAG 2.1 AA compliance
- **Performance:** <100ms response times
- **Offline Support:** Graceful degradation

---

## Success Metrics

### Functional Metrics
- [ ] All 12 major tasks completed successfully
- [ ] 95%+ test coverage achieved
- [ ] All performance targets met
- [ ] Zero critical security vulnerabilities

### User Experience Metrics
- [ ] Web interface loads in <2 seconds
- [ ] Real-time sync latency <500ms
- [ ] Migration completes in <5 minutes for 10k cards
- [ ] Offline functionality works seamlessly

### Technical Metrics
- [ ] RLS policies prevent unauthorized access
- [ ] Edge functions respond in <200ms
- [ ] Database queries optimized with proper indexes
- [ ] Memory usage within acceptable limits

This comprehensive plan positions Phase 2 for successful completion while establishing the foundation for advanced features in subsequent phases.

---

## Implementation Dependencies & Critical Path

### Critical Path Analysis
1. **Supabase Foundation** (Tasks 2.1.x) → **Authentication** (Tasks 2.2.x)
2. **Authentication** → **React Interface** (Tasks 2.3.x)
3. **React Interface** → **Real-time Sync** (Tasks 2.4.x)
4. **All Features** → **Migration Tools** (Tasks 2.5.x) → **Testing** (Task 2.6.x)

### Parallel Development Opportunities
- **Web Interface** (Tasks 2.3.x) can be developed in parallel with **Edge Functions** (Task 2.4.2)
- **Migration Tools** (Tasks 2.5.x) can be developed in parallel with **Real-time Sync** (Tasks 2.4.x)
- **Documentation** can be written incrementally alongside feature development

### Risk Mitigation Strategies

#### Technical Risks
- **Supabase Vendor Lock-in**
  - Mitigation: Maintain dual-mode architecture, implement data export tools
- **Real-time Sync Complexity**
  - Mitigation: Implement robust conflict resolution, extensive testing
- **Migration Data Loss**
  - Mitigation: Comprehensive backup systems, dry-run capabilities

#### Schedule Risks
- **Authentication Integration Complexity**
  - Mitigation: Start with basic email/password, add OAuth incrementally
- **Web Interface Development Time**
  - Mitigation: Use component libraries, focus on core functionality first

#### User Experience Risks
- **Migration Friction**
  - Mitigation: Automated migration tools, clear documentation, rollback options
- **Performance Degradation**
  - Mitigation: Performance benchmarks, optimization targets, monitoring

---

## Detailed Task Breakdown by Week

### Week 1: Supabase Foundation
- **Days 1-2:** Task 2.1.1 - Supabase setup and configuration
- **Days 3-5:** Task 2.1.2 - Schema migration and RLS setup

### Week 2: Dual-Mode Architecture
- **Days 1-5:** Task 2.1.3 - Dual-mode implementation and testing

### Week 3: Authentication Foundation
- **Days 1-3:** Task 2.2.1 - Supabase authentication integration
- **Days 4-5:** Task 2.2.2 - User profile management

### Week 4: Authentication Polish
- **Days 1-2:** Authentication testing and refinement
- **Days 3-5:** Begin React application setup (Task 2.3.1)

### Week 5: React Interface Core
- **Days 1-2:** Complete Task 2.3.1 - React setup
- **Days 3-5:** Task 2.3.2 - Authentication UI components

### Week 6: Review Interface
- **Days 1-5:** Task 2.3.3 - Review interface components

### Week 7: Real-time Features
- **Days 1-4:** Task 2.4.1 - Real-time synchronization
- **Day 5:** Begin Task 2.4.2 - Edge functions

### Week 8: Edge Functions & Optimization
- **Days 1-2:** Complete Task 2.4.2 - Edge functions
- **Days 3-5:** Performance optimization and testing

### Week 9: Migration Tools
- **Days 1-4:** Task 2.5.1 - Data migration CLI tool
- **Day 5:** Begin Task 2.5.2 - Backup systems

### Week 10: Final Integration & Testing
- **Days 1-2:** Complete Task 2.5.2 - Backup systems
- **Days 3-5:** Task 2.6.1 - Comprehensive testing and documentation

---

## Technology Stack & Dependencies

### Backend Technologies
- **Database:** Supabase (PostgreSQL with RLS)
- **Authentication:** Supabase Auth
- **Real-time:** Supabase Realtime
- **Edge Functions:** Supabase Edge Functions (Deno)
- **CLI:** Python 3.13+ with existing architecture

### Frontend Technologies
- **Framework:** React 18+ with TypeScript
- **Build Tool:** Vite
- **Styling:** Tailwind CSS
- **State Management:** Zustand
- **Routing:** React Router
- **HTTP Client:** Supabase JS client

### Development Tools
- **Testing:** Jest, React Testing Library, Playwright
- **Linting:** ESLint, Prettier
- **Type Checking:** TypeScript strict mode
- **CI/CD:** GitHub Actions
- **Monitoring:** Supabase Dashboard, Sentry

### Package Management Requirements
```bash
# Backend dependencies (add to pyproject.toml)
supabase = "^2.0.0"
python-dotenv = "^1.0.0"
pydantic = "^2.0.0"

# Frontend dependencies (package.json)
@supabase/supabase-js = "^2.0.0"
react = "^18.0.0"
typescript = "^5.0.0"
vite = "^5.0.0"
tailwindcss = "^3.0.0"
zustand = "^4.0.0"
```

---

## Security Considerations

### Authentication Security
- **Password Requirements:** Minimum 8 characters, complexity rules
- **Session Management:** Secure token storage, automatic expiration
- **OAuth Security:** Proper state validation, secure redirects
- **Rate Limiting:** Prevent brute force attacks

### Database Security
- **Row Level Security:** All tables properly secured
- **SQL Injection Prevention:** Parameterized queries only
- **Data Encryption:** Sensitive data encrypted at rest
- **Access Control:** Principle of least privilege

### Web Application Security
- **HTTPS Only:** All communications encrypted
- **CSRF Protection:** Proper token validation
- **XSS Prevention:** Input sanitization and CSP headers
- **Content Security Policy:** Strict CSP implementation

---

## Performance Targets & Monitoring

### Web Interface Performance
- **Initial Load:** <2 seconds for first meaningful paint
- **Review Session:** <100ms response time for card transitions
- **Search Results:** <500ms for complex queries
- **Offline Sync:** <1 second for sync completion

### Database Performance
- **Query Response:** <50ms for typical operations
- **Real-time Updates:** <200ms latency
- **Migration Speed:** >1000 cards/minute
- **Concurrent Users:** Support 100+ simultaneous users

### Monitoring & Alerting
- **Uptime Monitoring:** 99.9% availability target
- **Error Tracking:** Real-time error reporting
- **Performance Monitoring:** Response time tracking
- **Usage Analytics:** User behavior insights

---

## Documentation Requirements

### Technical Documentation
- **API Documentation:** Complete OpenAPI specification
- **Database Schema:** ERD and table documentation
- **Architecture Guide:** System design and data flow
- **Deployment Guide:** Step-by-step deployment instructions

### User Documentation
- **Migration Guide:** Step-by-step migration process
- **Web Interface Guide:** Feature documentation with screenshots
- **CLI Reference:** Updated command documentation
- **Troubleshooting Guide:** Common issues and solutions

### Developer Documentation
- **Contributing Guide:** Development setup and guidelines
- **Testing Guide:** Test writing and execution
- **Code Style Guide:** Coding standards and best practices
- **Release Process:** Version management and deployment

---

## Post-Phase 2 Preparation

### Phase 3 Readiness
- **Algorithm Framework:** Extensible algorithm interface for FSRS
- **A/B Testing Infrastructure:** Framework for algorithm comparison
- **Performance Metrics:** Baseline measurements for optimization

### Scalability Preparation
- **Database Optimization:** Query performance analysis
- **Caching Strategy:** Redis integration planning
- **CDN Setup:** Static asset optimization
- **Load Testing:** Capacity planning and stress testing

### Feature Foundation
- **Plugin Architecture:** Extensible system for future features
- **API Versioning:** Backward compatibility strategy
- **Data Analytics:** Event tracking infrastructure
- **Internationalization:** Multi-language support framework

This comprehensive Phase 2 implementation plan ensures a successful transition to a modern, scalable, multi-user spaced repetition platform while maintaining the high quality standards established in previous phases.
